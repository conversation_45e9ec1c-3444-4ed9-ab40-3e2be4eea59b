{"name": "bookeasy-portal", "private": true, "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint"}, "dependencies": {"@angular-devkit/build-angular": "^20.2.0", "@angular/animations": "^20.2.0", "@angular/cdk": "^20.2.0", "@angular/cli": "^20.2.0", "@angular/common": "^20.2.0", "@angular/compiler": "^20.2.0", "@angular/core": "^20.2.0", "@angular/forms": "^20.2.0", "@angular/material": "^20.2.0", "@angular/platform-browser": "^20.2.0", "@angular/platform-browser-dynamic": "^20.2.0", "@angular/router": "^20.2.0", "@ngneat/transloco": "^6.0.4", "@ngxpert/hot-toast": "^5.1.1", "@stripe/stripe-js": "^7.9.0", "@supabase/supabase-js": "^2.55.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "winston": "^3.17.0", "zone.js": "^0.15.1"}, "devDependencies": {"@angular-eslint/builder": "^20.1.1", "@angular-eslint/eslint-plugin": "^20.1.1", "@angular-eslint/eslint-plugin-template": "^20.1.1", "@angular-eslint/schematics": "^20.1.1", "@angular-eslint/template-parser": "^20.1.1", "@types/node": "^22.16.5", "autoprefixer": "^10.4.21", "eslint": "^9.32.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "typescript-eslint": "^8.38.0"}}