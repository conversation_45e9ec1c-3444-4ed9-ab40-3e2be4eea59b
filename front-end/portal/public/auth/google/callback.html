<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google OAuth Callback</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 400px;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .success {
            color: #27ae60;
        }
        .error {
            color: #e74c3c;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="spinner" id="spinner"></div>
        <h2 id="status">Processing Google Authentication...</h2>
        <p id="message">Please wait while we connect your Google account.</p>
    </div>

    <script>
        async function handleCallback() {
            const urlParams = new URLSearchParams(window.location.search);
            const code = urlParams.get('code');
            const error = urlParams.get('error');
            
            const statusEl = document.getElementById('status');
            const messageEl = document.getElementById('message');
            const spinnerEl = document.getElementById('spinner');

            if (error) {
                spinnerEl.style.display = 'none';
                statusEl.textContent = 'Authentication Failed';
                statusEl.className = 'error';
                messageEl.textContent = `Error: ${error}. You can close this window.`;
                return;
            }

            if (!code) {
                spinnerEl.style.display = 'none';
                statusEl.textContent = 'No Authorization Code';
                statusEl.className = 'error';
                messageEl.textContent = 'No authorization code received. You can close this window.';
                return;
            }

            try {
                // Send the code to your instantbookr.test backend
                const response = await fetch('http://instantbookr.test/api/v1/google/callback', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json',
                        // Add any auth headers if needed
                    },
                    body: JSON.stringify({ code: code })
                });

                const result = await response.json();
                
                spinnerEl.style.display = 'none';
                
                if (result.success) {
                    statusEl.textContent = 'Successfully Connected!';
                    statusEl.className = 'success';
                    messageEl.textContent = `Your Google account (${result.google_email || 'account'}) has been connected. You can close this window.`;
                    
                    // Auto-close after 3 seconds
                    setTimeout(() => {
                        window.close();
                    }, 3000);
                } else {
                    statusEl.textContent = 'Connection Failed';
                    statusEl.className = 'error';
                    messageEl.textContent = `Error: ${result.error || 'Unknown error occurred'}. You can close this window.`;
                }
            } catch (error) {
                spinnerEl.style.display = 'none';
                statusEl.textContent = 'Connection Failed';
                statusEl.className = 'error';
                messageEl.textContent = `Network error: ${error.message}. Please try again later.`;
                console.error('OAuth callback error:', error);
            }
        }

        // Run when page loads
        handleCallback();
    </script>
</body>
</html>