import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Appointment } from './appointment.model';
import { ConfigService } from '../../app/services/config.service';

@Injectable({
  providedIn: 'root',
})
export class AppointmentService {
  private http = inject(HttpClient);
  private configService = inject(ConfigService);
  private endpoint = this.configService.config?.environment.endpoint;

  getAppointments(): Observable<{ data: Appointment[] }> {
    return this.http.get<{ data: Appointment[] }>(`${this.endpoint}/api/v1/appointments`);
  }

  getAppointment(id: number): Observable<Appointment> {
    return this.http.get<Appointment>(`${this.endpoint}/api/v1/appointments/${id}`);
  }

  updateAppointment(id: number, data: Partial<Appointment>): Observable<any> {
    return this.http.put(`${this.endpoint}/api/v1/appointments/${id}`, data);
  }

  cancelAppointment(id: number): Observable<any> {
    return this.http.patch(`${this.endpoint}/api/v1/appointments/${id}/cancel`, {});
  }

  deleteAppointment(id: number): Observable<any> {
    return this.http.delete(`${this.endpoint}/api/v1/appointments/${id}`);
  }
}