import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ConfigService } from '../../../app/services/config.service';
import { LoginRequest } from '../requests/login.request';
import { Observable } from 'rxjs';
// import { RegisterRequest } from '../requests/register.request';
import { Me } from '../models/me.model';
import { DataResponse } from '../../support/responses/data.response';
import {RegisterRequest} from "@api/auth/requests/register.request";

@Injectable({
  providedIn: 'root',
})
export class AuthenticationService {
  private readonly endpoint?: string;

  constructor(
    private httpClient: HttpClient,
    private configService: ConfigService,
  ) {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public login(body: LoginRequest): Observable<void> {
    return this.httpClient.post<void>(
      `${this.endpoint}/api/v1/auth/login`,
      body,
    );
  }

  public token(): Observable<void> {
    return this.httpClient.get<void>(`${this.endpoint}/sanctum/csrf-cookie`);
  }

  public logout(): Observable<void> {
    return this.httpClient.post<void>(
      `${this.endpoint}/api/v1/auth/logout`,
      {},
    );
  }

  public register(body: RegisterRequest): Observable<void> {
    return this.httpClient.post<void>(
      `${this.endpoint}/api/v1/auth/register`,
      body,
    );
  }

  public me(): Observable<DataResponse<Me>> {
    return this.httpClient.get<DataResponse<Me>>(
      `${this.endpoint}/api/v1/auth/me`,
    );
  }
}
