export interface BookingCalendar {
    id: number;
    account_id: number;
    name: string;
    description?: string;
    duration: number;
    color: string;
    slug: string;
    is_active: boolean;
    conversion_tracking: boolean;
    google_meet_integration: boolean;
    whats_next_section: boolean;
    created_at: string;
    updated_at: string;
    schedules?: Schedule[];
}

export interface Schedule {
    id?: number;
    calendar_id: number;
    day_of_week: string;
    time_slots: TimeSlot[];
    created_at?: string;
    updated_at?: string;
}

export interface TimeSlot {
    id?: number;
    schedule_id?: number;
    start_time: string;
    end_time: string;
    created_at?: string;
    updated_at?: string;
}

export interface CreateBookingCalendarRequest {
    name: string;
    description?: string;
    duration: number;
    color: string; // Must be one of: indigo, blue, green, red, yellow, purple
    conversion_tracking?: boolean;
    google_meet_integration?: boolean;
    whats_next_section?: boolean;
    schedules?: CreateScheduleRequest[];
}

export interface CreateScheduleRequest {
    day_of_week: string;
    time_slots: CreateTimeSlotRequest[];
}

export interface CreateTimeSlotRequest {
    start_time: string;
    end_time: string;
}

export interface UpdateBookingCalendarRequest {
    name?: string;
    description?: string;
    duration?: number;
    color?: string;
    conversion_tracking?: boolean;
    google_meet_integration?: boolean;
    whats_next_section?: boolean;
    is_active?: boolean;
}