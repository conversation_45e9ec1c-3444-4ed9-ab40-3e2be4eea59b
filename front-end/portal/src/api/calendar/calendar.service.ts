import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { ConfigService } from '../../app/services/config.service';
import { BookingCalendar, CreateBookingCalendarRequest, UpdateBookingCalendarRequest } from './calendar.interface';

@Injectable({
    providedIn: 'root'
})
export class CalendarService {
    private readonly endpoint?: string;

    constructor(
        private http: HttpClient,
        private configService: ConfigService
    ) {
        this.endpoint = this.configService.config?.environment.endpoint;
    }

    index(): Observable<BookingCalendar[]> {
        return this.http.get<BookingCalendar[]>(`${this.endpoint}/api/v1/calendars`);
    }

    show(id: number): Observable<BookingCalendar> {
        return this.http.get<BookingCalendar>(`${this.endpoint}/api/v1/calendars/${id}`);
    }

    store(data: CreateBookingCalendarRequest): Observable<BookingCalendar> {
        return this.http.post<BookingCalendar>(`${this.endpoint}/api/v1/calendars`, data);
    }

    update(calendar: BookingCalendar, data: UpdateBookingCalendarRequest): Observable<BookingCalendar> {
        return this.http.put<BookingCalendar>(`${this.endpoint}/api/v1/calendars/${calendar.id}`, data);
    }

    delete(id: number): Observable<{ message: string }> {
        return this.http.delete<{ message: string }>(`${this.endpoint}/api/v1/calendars/${id}`);
    }

    getBySlug(slug: string): Observable<BookingCalendar> {
        return this.http.get<BookingCalendar>(`${this.endpoint}/api/v1/calendars/slug/${slug}`);
    }

    getSignedCalendarUrl(slug: string): Observable<{ url: string }> {
        return this.http.get<{ url: string }>(`${this.endpoint}/api/v1/calendars/${slug}/signed-url`);
    }
}