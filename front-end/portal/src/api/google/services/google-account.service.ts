import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { IndexRequest } from '@api/support/requests/index.request';
import { DataResponse } from '@api/support/responses/data.response';
import { ConfigService } from '@app/services/config.service';
import { Observable } from 'rxjs';
import { GoogleAccount } from '../models/google-account.interface';
import { PaginatedResponse } from '@api/support/responses/paginated.response';
import {paramsToHttpParams} from "@helpers/transform-params";


@Injectable({
  providedIn: 'root',
})
export class GoogleAccountService {
  private readonly endpoint?: string;

  constructor(
    private configService: ConfigService,
    private httpClient: HttpClient,
  ) {
    this.endpoint = this.configService.config?.environment.endpoint;
  }

  public index(
    request: IndexRequest,
  ): Observable<
    DataResponse<GoogleAccount[]> | PaginatedResponse<GoogleAccount>
  > {
    const params = paramsToHttpParams(request);

    return this.httpClient.get<
      DataResponse<GoogleAccount[]> | PaginatedResponse<GoogleAccount>
    >(`${this.endpoint}/api/v1/google/accounts`, { params });
  }

  public delete(account: GoogleAccount): Observable<void> {
    return this.httpClient.delete<void>(
      `${this.endpoint}/api/v1/google/accounts/${account.id}`,
    );
  }
}
