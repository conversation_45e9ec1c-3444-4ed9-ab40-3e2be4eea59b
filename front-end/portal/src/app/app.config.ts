import { ApplicationConfig, importProvidersFrom, inject, provideAppInitializer } from '@angular/core';
import { provideRouter, withComponentInputBinding, withHashLocation } from '@angular/router';
import {provideHttpClient, withInterceptors, withInterceptorsFromDi} from '@angular/common/http';
import { provideAnimations } from '@angular/platform-browser/animations';
import { TranslocoModule, provideTransloco } from '@ngneat/transloco';

import { routes } from './app.routes';
import { TranslocoHttpLoader } from './services/transloco-loader.service';
import { ConfigService } from './services/config.service';
import {unauthenticatedInterceptor} from "@interceptors/unauthenticated.interceptor";

export const appConfig: ApplicationConfig = {
  providers: [
    provideRouter(routes, withHashLocation(), withComponentInputBinding()),
      provideHttpClient(withInterceptors([unauthenticatedInterceptor])),
    provideAnimations(),
    provideTransloco({
      config: {
        availableLangs: ['en'],
        defaultLang: 'en',
        fallbackLang: 'en',
        prodMode: true,
      },
      loader: TranslocoHttpLoader
    }),
    importProvidersFrom(TranslocoModule),
    provideAppInitializer(() => {
      const initializerFn = (
        (configService: ConfigService) => () =>
          configService.initialize().then(async ({}) => {})
      )(inject(ConfigService));
      return initializerFn();
    }),
  ]
};
