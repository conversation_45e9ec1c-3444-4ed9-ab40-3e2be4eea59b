import { Routes } from '@angular/router';
import { authGuard } from './guards/auth.guard';
import { AuthComponent } from './pages/auth/auth.component';
import {ContainerComponent} from "@layout/container/container.component";

export const routes: Routes = [
  // OAuth callback route (no auth required)
  {
    path: 'auth/google/callback',
    loadComponent: () => import('./pages/oauth/google-callback.component').then(m => m.GoogleCallbackComponent)
  },
  {
    path: '',
    canActivate: [authGuard],
    component: ContainerComponent,
    children: [
        {
            path: '',
            pathMatch: 'full',
            redirectTo: 'calendars',
        },
        {
            path: 'calendars',
            loadChildren: () => import('./pages/calendars/calendars.routes').then((r) => r.routes)
        },
        {
            path: 'appointments',
            loadComponent: () => import('./pages/appointments/appointments.component').then(m => m.AppointmentsComponent)
        },
        {
            path: 'settings',
            loadChildren: () => import('./pages/settings/settings.routes').then((r) => r.routes)
        },
    ]
  },
  {
    path: 'auth',
    component: AuthComponent,
      loadChildren: () => import('./pages/auth/auth.routes').then((r) => r.routes)
  },

  // Redirect /auth to /auth/login
  { path: 'auth', redirectTo: 'auth/login' },
  // Redirect everything else to home
  { path: '**', redirectTo: '' },
];
