import { Routes } from '@angular/router';
import { authGuard } from './guards/auth.guard';
import { AuthComponent } from './pages/auth/auth.component';
import {ContainerComponent} from "@layout/container/container.component";

export const routes: Routes = [
  {
    path: '',
    canActivate: [authGuard],
    component: ContainerComponent,
    children: [
        {
            path: '',
            pathMatch: 'full',
            redirectTo: 'calendars',
        },
        {
            path: 'calendars',
            loadChildren: () => import('./pages/calendars/calendars.routes').then((r) => r.routes)
        },
    ]
  },
  {
    path: 'auth',
    component: AuthComponent,
      loadChildren: () => import('./pages/auth/auth.routes').then((r) => r.routes)
  },

  // Redirect /auth to /auth/login
  { path: 'auth', redirectTo: 'auth/login' },
  // Redirect everything else to home
  { path: '**', redirectTo: '' },
];
