import { RadialChartResponse } from '@api/dashboard/responses/widget/radial-chart.response';
import { DataComparisonResponse } from '@api/dashboard/responses/widget/data-comparison.response';
import {
  ChartResponse,
  ChartValueResponse,
} from '@api/dashboard/responses/widget/chart.response';
import { WidgetAccuracy } from '@api/dashboard/support/enums/widget-accuracy.enum';
import { format } from 'date-fns';
import {
  PieChartResponse,
  PieChartSeriesResponse,
} from '@api/dashboard/responses/widget/pie-chart.response';

export const generateRadialStatistics = (): RadialChartResponse => {
  const value = Math.floor(Math.random() * 100);

  return {
    min: '0',
    max: '100',
    prefix: null,
    suffix: '%',
    series: [
      {
        percentage: value,
        formatted_value: value.toString(),
        title: null,
        prefix: null,
        suffix: '%',
      },
    ],
  };
};

export const generateDataComparison = (): DataComparisonResponse => {
  const value = Math.floor(Math.random() * 10 * (Math.random() * 10));
  const comparison = Math.floor(Math.random() * 10 * (Math.random() * 10));

  return {
    value,
    value_formatted: value.toString(),
    comparison,
    comparison_formatted: comparison.toString(),
  };
};

export const generateChartStatistics = (): ChartResponse => {
  const generateSeriesData = (days: number): ChartValueResponse[] => {
    const data: ChartValueResponse[] = [];

    for (let day = 0; day < days; day++) {
      const date = new Date();
      const value = Math.floor(Math.random() * 100);
      data.push({
        x: format(date.setDate(date.getDate() - day), 'yyyy-MM-dd'),
        y: value,
        prefix: null,
        suffix: null,
        value_formatted: value.toString(),
      });
    }

    return data;
  };

  return {
    accuracy: WidgetAccuracy.DAY,
    name: 'Whitelabel',
    series: [
      {
        name: 'Whitelabel series',
        data: generateSeriesData(15),
      },
    ],
    anomalies: [],
  };
};

export const generatePieChartStatistics = (): PieChartResponse => {
  const generateSeries = (amount: number): PieChartSeriesResponse[] => {
    const value = Math.floor(Math.random() * 100);

    const data: PieChartSeriesResponse[] = [];

    for (let index = 0; index < amount; index++) {
      data.push({
        label: `whitelabel-${index}`,
        value,
        formatted_value: value.toString(),
        color: '',
      });
    }

    return data;
  };

  return {
    series: generateSeries(3),
  };
};
