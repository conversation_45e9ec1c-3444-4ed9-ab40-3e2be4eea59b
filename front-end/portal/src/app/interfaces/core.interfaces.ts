// Core interfaces following Angular constitution - strict typing required

export interface User {
  id: string;
  email: string;
  created_at: string;
  updated_at: string;
}

export interface Session {
  access_token: string;
  token_type: string;
  expires_in: number;
  refresh_token: string;
  user: User;
}

export interface Calendar {
  id: string;
  name: string;
  description: string | null;
  user_id: string;
  slug: string;
  duration_minutes: number;
  is_active: boolean;
  timezone: string;
  created_at: string;
  updated_at: string;
  booking_count?: number;
}

export interface Booking {
  id: string;
  calendar_id: string;
  attendee_name: string;
  attendee_email: string;
  attendee_phone: string | null;
  attendee_message: string | null;
  start_time: string;
  end_time: string;
  status: BookingStatus;
  created_at: string;
  updated_at: string;
  meet_link?: string;
  calendars?: Calendar;
}

export interface Contact {
  attendee_name: string;
  attendee_email: string;
  booking_count: number;
  latest_booking: string;
  calendar_names: string[];
}

export interface TimeSlot {
  time: string;
  available: boolean;
}

export interface AvailabilityRule {
  id: string;
  day_of_week: number;
  start_time: string;
  end_time: string;
}

export interface AvailabilitySchedule {
  id: string;
  name: string;
  description: string | null;
  is_default: boolean;
  rules: AvailabilityRule[];
}

export interface GoogleCalendarIntegration {
  id: string;
  google_account_email: string;
  sync_enabled: boolean;
  last_sync_at: string | null;
  calendar_id: string | null;
}

export interface AppleCalendarIntegration {
  id: string;
  account_name: string;
  caldav_url: string;
  username: string;
  sync_enabled: boolean;
  last_sync_at: string | null;
  calendar_id: string | null;
}

export interface HeaderSettings {
  header_title: string;
  header_description: string;
  header_image_url: string;
  show_header: boolean;
}

export interface ThankYouConfig {
  enabled: boolean;
  title: string;
  message: string;
  show_booking_details: boolean;
  redirect_enabled: boolean;
  redirect_url: string;
  redirect_delay: number;
  custom_html: string;
  upsells: {
    enabled: boolean;
    items: Array<{
      title: string;
      description: string;
      price: string;
      button_text: string;
      button_url: string;
    }>;
  };
}

export enum BookingStatus {
  CONFIRMED = 'confirmed',
  CANCELLED = 'cancelled',
  PENDING = 'pending'
}

// Auth context interface
export interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  signOut: () => Promise<void>;
}
