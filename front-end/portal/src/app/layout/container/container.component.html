<div class="min-h-screen bg-gray-100 flex">
    <!-- Sidebar -->
    <div class="w-80 bg-white border-r border-gray-200 shadow-sm">
        <!-- Sidebar Header -->
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                    <span class="text-white font-bold text-sm">B</span>
                </div>
                <span class="text-gray-900 font-semibold text-lg">InstantBookr</span>
            </div>
        </div>

        <!-- Navigation Menu -->
        <nav class="p-4">
            <ul class="space-y-2">
                @for (item of navigationItems; track item.route) {
                    <li>
                        <button
                                [routerLink]="item.route"
                                routerLinkActive="!bg-orange-50 !text-orange-700"
                                class="w-full flex items-center space-x-3 px-4 py-3 text-left rounded-lg transition-colors text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                        >
                            <i [class]="item.icon"></i>
                            <span class="font-medium">{{ item.label }}</span>
                        </button>
                    </li>
                }
            </ul>
        </nav>
    </div>

    <div class="w-full">
        <router-outlet></router-outlet>
    </div>
</div>