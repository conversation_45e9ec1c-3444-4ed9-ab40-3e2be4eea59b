import { Component } from '@angular/core';
import {RouterLink, RouterLinkActive, RouterOutlet} from "@angular/router";

@Component({
  selector: 'app-container',
    imports: [
        RouterOutlet,
        RouterLink,
        RouterLinkActive
    ],
  templateUrl: './container.component.html',
})
export class ContainerComponent {
    public navigationItems = [
        // { icon: 'fas fa-user', label: 'Account', route: '/account', active: false },
        { icon: 'fas fa-calendar', label: 'Calendars', route: '/calendars', active: true }
    //     { icon: 'fas fa-cog', label: 'Account Settings', route: '/settings', active: false },
    //     { icon: 'fas fa-tools', label: 'Tools', route: '/tools', active: false },
    //     { icon: 'fas fa-chart-bar', label: 'Analytics', route: '/analytics', active: false },
    //     { icon: 'fas fa-handshake', label: 'Meetings', route: '/meetings', active: false },
    //     { icon: 'fas fa-users', label: 'Contacts', route: '/contacts', active: false },
    //     { icon: 'fas fa-clock', label: 'Availability', route: '/availability', active: false },
    //     { icon: 'fas fa-calendar', label: 'Google Calendar', route: '/google-calendar', active: false },
    //     { icon: 'fab fa-apple', label: 'Apple Calendar', route: '/apple-calendar', active: false },
    ];
}
