<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Header -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900">Appointments</h1>
    <p class="mt-2 text-gray-600">Manage all your calendar appointments</p>
  </div>

  <!-- Loading State -->
  @if (loading()) {
    <div class="flex items-center justify-center h-64">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500"></div>
    </div>
  }

  <!-- Error State -->
  @else if (error()) {
    <div class="text-center py-16">
      <div class="text-red-600 text-xl font-semibold">{{ error() }}</div>
      <button 
        (click)="loadAppointments()"
        class="mt-4 px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600">
        Try Again
      </button>
    </div>
  }

  <!-- Appointments List -->
  @else if (appointments().length === 0) {
    <div class="text-center py-16">
      <div class="text-gray-500 text-xl">No appointments found</div>
      <p class="mt-2 text-gray-400">Appointments will appear here when customers book time slots</p>
    </div>
  }

  @else {
    <div class="bg-white shadow overflow-hidden sm:rounded-lg">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Customer
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Calendar
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date & Time
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            @for (appointment of appointments(); track appointment.id) {
              <tr class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div class="text-sm font-medium text-gray-900">{{ appointment.customer_name }}</div>
                    <div class="text-sm text-gray-500">{{ appointment.customer_email }}</div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ appointment.calendar?.name }}</div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="text-sm text-gray-900">{{ formatDate(appointment.date) }}</div>
                  <div class="text-sm text-gray-500">
                    {{ formatTime(appointment.start_time) }} - {{ formatTime(appointment.end_time) }}
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span [class]="'inline-flex px-2 py-1 text-xs font-semibold rounded-full ' + getStatusBadgeClass(appointment.status)">
                    {{ appointment.status | titlecase }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                  <button 
                    (click)="viewAppointment(appointment)"
                    class="text-orange-600 hover:text-orange-900">
                    View
                  </button>
                  @if (appointment.status === 'confirmed') {
                    <button 
                      (click)="cancelAppointment(appointment)"
                      class="text-yellow-600 hover:text-yellow-900">
                      Cancel
                    </button>
                  }
                  <button 
                    (click)="deleteAppointment(appointment)"
                    class="text-red-600 hover:text-red-900">
                    Delete
                  </button>
                </td>
              </tr>
            }
          </tbody>
        </table>
      </div>
    </div>
  }

  <!-- Appointment Details Modal -->
  @if (showDetailsModal() && selectedAppointment()) {
    <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <!-- Modal Header -->
          <div class="flex items-center justify-between pb-4 border-b">
            <h3 class="text-lg font-medium text-gray-900">Appointment Details</h3>
            <button 
              (click)="closeDetailsModal()"
              class="text-gray-400 hover:text-gray-600">
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          <!-- Modal Content -->
          <div class="mt-4 space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700">Customer Name</label>
                <p class="mt-1 text-sm text-gray-900">{{ selectedAppointment()?.customer_name }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">Email</label>
                <p class="mt-1 text-sm text-gray-900">{{ selectedAppointment()?.customer_email }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">Calendar</label>
                <p class="mt-1 text-sm text-gray-900">{{ selectedAppointment()?.calendar?.name }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">Status</label>
                <span [class]="'inline-flex px-2 py-1 text-xs font-semibold rounded-full ' + getStatusBadgeClass(selectedAppointment()?.status || '')">
                  {{ selectedAppointment()?.status | titlecase }}
                </span>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">Date</label>
                <p class="mt-1 text-sm text-gray-900">{{ formatDate(selectedAppointment()?.date || '') }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700">Time</label>
                <p class="mt-1 text-sm text-gray-900">
                  {{ formatTime(selectedAppointment()?.start_time || '') }} - 
                  {{ formatTime(selectedAppointment()?.end_time || '') }}
                </p>
              </div>
            </div>
            
            @if (selectedAppointment()?.notes) {
              <div>
                <label class="block text-sm font-medium text-gray-700">Notes</label>
                <p class="mt-1 text-sm text-gray-900 bg-gray-50 p-3 rounded">{{ selectedAppointment()?.notes }}</p>
              </div>
            }
          </div>

          <!-- Modal Actions -->
          <div class="mt-6 flex justify-end space-x-3">
            <button 
              (click)="closeDetailsModal()"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200">
              Close
            </button>
            @if (selectedAppointment()?.status === 'confirmed') {
              <button 
                (click)="cancelAppointment(selectedAppointment()!); closeDetailsModal()"
                class="px-4 py-2 text-sm font-medium text-white bg-yellow-600 border border-transparent rounded-md hover:bg-yellow-700">
                Cancel Appointment
              </button>
            }
          </div>
        </div>
      </div>
    </div>
  }