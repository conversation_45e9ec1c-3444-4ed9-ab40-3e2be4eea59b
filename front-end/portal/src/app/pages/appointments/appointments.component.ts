import { Component, OnInit, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AppointmentService } from '../../../api/appointment/appointment.service';
import { Appointment } from '../../../api/appointment/appointment.model';

@Component({
  selector: 'app-appointments',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './appointments.component.html',
  styleUrls: ['./appointments.component.scss'],
})
export class AppointmentsComponent implements OnInit {
  private appointmentService = inject(AppointmentService);

  public appointments = signal<Appointment[]>([]);
  public loading = signal<boolean>(false);
  public error = signal<string | null>(null);
  public selectedAppointment = signal<Appointment | null>(null);
  public showDetailsModal = signal<boolean>(false);

  ngOnInit(): void {
    this.loadAppointments();
  }

  async loadAppointments(): Promise<void> {
    this.loading.set(true);
    this.error.set(null);

    try {
      const response = await this.appointmentService.getAppointments().toPromise();
      this.appointments.set(response?.data || []);
    } catch (error) {
      console.error('Error loading appointments:', error);
      this.error.set('Failed to load appointments');
    } finally {
      this.loading.set(false);
    }
  }

  viewAppointment(appointment: Appointment): void {
    this.selectedAppointment.set(appointment);
    this.showDetailsModal.set(true);
  }

  closeDetailsModal(): void {
    this.showDetailsModal.set(false);
    this.selectedAppointment.set(null);
  }

  async cancelAppointment(appointment: Appointment): Promise<void> {
    if (!confirm('Are you sure you want to cancel this appointment?')) {
      return;
    }

    try {
      await this.appointmentService.cancelAppointment(appointment.id).toPromise();
      await this.loadAppointments(); // Refresh the list
    } catch (error) {
      console.error('Error cancelling appointment:', error);
      alert('Failed to cancel appointment');
    }
  }

  async deleteAppointment(appointment: Appointment): Promise<void> {
    if (!confirm('Are you sure you want to delete this appointment? This action cannot be undone.')) {
      return;
    }

    try {
      await this.appointmentService.deleteAppointment(appointment.id).toPromise();
      await this.loadAppointments(); // Refresh the list
    } catch (error) {
      console.error('Error deleting appointment:', error);
      alert('Failed to delete appointment');
    }
  }

  getStatusBadgeClass(status: string): string {
    switch (status) {
      case 'confirmed':
        return 'bg-green-100 text-green-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      case 'completed':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }

  formatDate(date: string): string {
    return new Date(date).toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  formatTime(time: string): string {
    return new Date(`2000-01-01T${time}`).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  }
}