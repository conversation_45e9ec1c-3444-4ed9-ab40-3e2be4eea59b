import { Component, inject, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule, FormGroup, FormControl } from '@angular/forms';
import { AuthenticationService } from '../../../api/auth/services/authentication.service';
import { RouterOutlet } from '@angular/router';


@Component({
  selector: 'app-auth',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterOutlet],
  templateUrl: './auth.component.html',
  styleUrls: ['./auth.component.scss'],
})
export class AuthComponent implements OnInit {
  private authenticationService = inject(AuthenticationService);

  public ngOnInit(): void {
    this.authenticationService.token().subscribe()
  }
}
