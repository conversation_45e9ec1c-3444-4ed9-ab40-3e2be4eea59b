<div class="min-h-screen flex items-center justify-center bg-gray-50">
  <div class="max-w-md w-full space-y-8">
    <div>
      <div class="flex items-center justify-center space-x-2 mb-8">
        <div class="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center">
          <span class="text-white font-bold text-lg">B</span>
        </div>
        <span class="text-2xl font-bold text-gray-900">BookEasy Pro</span>
      </div>
      <h2 class="text-center text-3xl font-extrabold text-gray-900">
        Sign in to your account
      </h2>
    </div>

    @if(form(); as loginForm) {
      <form class="mt-8 space-y-6" [formGroup]="loginForm" (ngSubmit)="onSubmit()">
        <div class="space-y-4">
          <div>
            <label for="email" class="block text-sm font-medium text-gray-700">
              Email address
            </label>
            <input
              id="email"
              name="email"
              type="email"
              autocomplete="email"
              required
              formControlName="email"
              class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500 focus:z-10 sm:text-sm"
              placeholder="Enter your email"
            />
            @if(loginForm.controls.email.invalid && loginForm.controls.email.touched) {
              <p class="mt-1 text-sm text-red-600">Please enter a valid email address.</p>
            }
          </div>

          <div>
            <label for="password" class="block text-sm font-medium text-gray-700">
              Password
            </label>
            <input
              id="password"
              name="password"
              type="password"
              autocomplete="current-password"
              required
              formControlName="password"
              class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-orange-500 focus:border-orange-500 focus:z-10 sm:text-sm"
              placeholder="Enter your password"
            />
            @if(loginForm.controls.password.invalid && loginForm.controls.password.touched) {
              <p class="mt-1 text-sm text-red-600">Password is required.</p>
            }
          </div>

          <div class="flex items-center">
            <input
              id="remember"
              name="remember"
              type="checkbox"
              formControlName="remember"
              class="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
            />
            <label for="remember" class="ml-2 block text-sm text-gray-900">
              Remember me
            </label>
          </div>
        </div>

        @if(error()) {
          <div class="rounded-md bg-red-50 p-4">
            <div class="text-sm text-red-700">
              {{ error() }}
            </div>
          </div>
        }

        <div>
          <button
            type="submit"
            [disabled]="loading() || loginForm.invalid"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            @if(loading()) {
              <span class="mr-2">Signing in...</span>
            } @else {
              <span>Sign in</span>
            }
          </button>
        </div>

        <div class="text-center space-y-3">
          <p class="text-sm text-gray-600">
            Don't have an account? 
            <a routerLink="/auth/register" class="font-medium text-orange-600 hover:text-orange-500">
              Create one here
            </a>
          </p>
          <p class="text-sm text-gray-600">
            Test credentials: <EMAIL> / password
          </p>
          <button
            type="button"
            (click)="fillTestCredentials()"
            class="text-sm text-orange-600 hover:text-orange-800 underline"
          >
            Click to fill test credentials
          </button>
        </div>
      </form>
    }
  </div>
</div>
