import { Component, signal, inject } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { AuthState, type LoginCredentials } from '../../../states/auth.state';

// Form interface (following project rules)
interface LoginForm {
  email: FormControl<string | null>;
  password: FormControl<string | null>;
  remember: FormControl<boolean | null>;
}

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterModule],
  templateUrl: './login.component.html',
  styleUrl: './login.component.scss'
})
export class LoginComponent {
  private authState = inject(AuthState);
  private router = inject(Router);

  // Form signal (following project rules)
  private readonly formSignal = signal<FormGroup<LoginForm> | null>(null);
  readonly form = this.formSignal.asReadonly();

  // Loading and error states
  readonly loading = this.authState.loading;
  private readonly errorSignal = signal<string | null>(null);
  readonly error = this.errorSignal.asReadonly();

  constructor() {
    this.initForm();
  }

  private initForm(): void {
    const form = new FormGroup<LoginForm>({
      email: new FormControl(null, [Validators.required, Validators.email]),
      password: new FormControl(null, [Validators.required]),
      remember: new FormControl(false)
    });
    this.formSignal.set(form);
  }

  onSubmit(): void {
    const form = this.form();
    if (!form || form.invalid) {
      return;
    }

    this.errorSignal.set(null);
    
    const credentials: LoginCredentials = {
      email: form.value.email!,
      password: form.value.password!,
      remember: form.value.remember || false
    };

    this.authState.login(credentials).subscribe({
      next: () => {
        // Redirect to index on successful login
        this.router.navigate(['/dashboard']);
      },
      error: (error: any) => {
        console.error('Login error:', error);
        this.errorSignal.set(
          error.error?.message || 'Login failed. Please check your credentials.'
        );
      }
    });
  }

  fillTestCredentials(): void {
    const form = this.form();
    if (form) {
      form.patchValue({
        email: '<EMAIL>',
        password: 'password'
      });
    }
  }
}
