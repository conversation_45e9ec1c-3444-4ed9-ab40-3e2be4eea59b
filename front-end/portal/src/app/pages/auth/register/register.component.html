<!-- Register Page -->
<div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
  <div class="sm:mx-auto sm:w-full sm:max-w-md">
    <div class="flex justify-center">
      <div class="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center">
        <span class="text-white font-bold text-xl">B</span>
      </div>
    </div>
    <h2 class="mt-6 text-center text-3xl font-bold text-gray-900">
      Create your account
    </h2>
    <p class="mt-2 text-center text-sm text-gray-600">
      Already have an account?
      <a routerLink="/auth/login" class="font-medium text-orange-600 hover:text-orange-500">
        Sign in here
      </a>
    </p>
  </div>

  <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
    <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
      <form (ngSubmit)="onSubmit()" #registerForm="ngForm">
        <!-- Name Field -->
        <div class="mb-6">
          <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
            Full Name
          </label>
          <input
            type="text"
            id="name"
            name="name"
            [(ngModel)]="formData.name"
            required
            #name="ngModel"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-orange-500 focus:border-orange-500"
            placeholder="Enter your full name"
          />
          @if (name.invalid && name.touched) {
            <p class="mt-1 text-sm text-red-600">Name is required</p>
          }
        </div>

        <!-- Email Field -->
        <div class="mb-6">
          <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
            Email Address
          </label>
          <input
            type="email"
            id="email"
            name="email"
            [(ngModel)]="formData.email"
            required
            email
            #email="ngModel"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-orange-500 focus:border-orange-500"
            placeholder="Enter your email address"
          />
          @if (email.invalid && email.touched) {
            <p class="mt-1 text-sm text-red-600">
              @if (email.errors?.['required']) {
                Email is required
              }
              @if (email.errors?.['email']) {
                Please enter a valid email address
              }
            </p>
          }
        </div>

        <!-- Password Field -->
        <div class="mb-6">
          <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
            Password
          </label>
          <input
            type="password"
            id="password"
            name="password"
            [(ngModel)]="formData.password"
            required
            minlength="8"
            #password="ngModel"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-orange-500 focus:border-orange-500"
            placeholder="Enter your password"
          />
          @if (password.invalid && password.touched) {
            <p class="mt-1 text-sm text-red-600">
              @if (password.errors?.['required']) {
                Password is required
              }
              @if (password.errors?.['minlength']) {
                Password must be at least 8 characters long
              }
            </p>
          }
        </div>

        <!-- Confirm Password Field -->
        <div class="mb-6">
          <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">
            Confirm Password
          </label>
          <input
            type="password"
            id="password_confirmation"
            name="password_confirmation"
            [(ngModel)]="formData.password_confirmation"
            required
            #passwordConfirmation="ngModel"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-orange-500 focus:border-orange-500"
            placeholder="Confirm your password"
          />
          @if (passwordConfirmation.invalid && passwordConfirmation.touched) {
            <p class="mt-1 text-sm text-red-600">Please confirm your password</p>
          }
          @if (formData.password_confirmation && formData.password !== formData.password_confirmation) {
            <p class="mt-1 text-sm text-red-600">Passwords do not match</p>
          }
        </div>

        <!-- Error Message -->
        @if (error()) {
          <div class="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
            <p class="text-sm text-red-600">{{ error() }}</p>
          </div>
        }

        <!-- Terms -->
        <div class="mb-6">
          <p class="text-xs text-gray-500">
            By creating an account, you agree to our 
            <a href="#" class="text-orange-600 hover:text-orange-500">Terms of Service</a> 
            and 
            <a href="#" class="text-orange-600 hover:text-orange-500">Privacy Policy</a>.
            A Stripe customer account will be created for billing purposes.
          </p>
        </div>

        <!-- Submit Button -->
        <div>
          <button
            type="submit"
            [disabled]="loading() || registerForm.invalid"
            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            @if (loading()) {
              <div class="flex items-center">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creating Account...
              </div>
            } @else {
              Create Account
            }
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
