import {Component, DestroyRef, signal} from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { AuthService } from '@app/services/auth.service';
import { HttpClient } from '@angular/common/http';
import {AuthenticationService} from "@api/auth/services/authentication.service";
import {catchError, tap} from "rxjs";
import {takeUntilDestroyed} from "@angular/core/rxjs-interop";

interface RegisterResponse {
  user: {
    id: number;
    name: string;
    email: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
  };
  message: string;
  stripe_customer_id?: string;
}

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [CommonModule, FormsModule, RouterModule],
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.scss']
})
export class RegisterComponent {
  loading = signal(false);
  error = signal<string | null>(null);
  
  formData = {
    name: '',
    email: '',
    password: '',
    password_confirmation: ''
  };

  constructor(
    private authService: AuthService,
    private router: Router,
    private http: HttpClient,
    private authenticationService: AuthenticationService,
    private destroyRef: DestroyRef
  ) {}

  public onSubmit(): void {
    this.error.set(null);
    
    // Basic validation
    if (!this.formData.name || !this.formData.email || !this.formData.password) {
      this.error.set('Please fill in all fields');
      return;
    }

    if (this.formData.password !== this.formData.password_confirmation) {
      this.error.set('Passwords do not match');
      return;
    }

    if (this.formData.password.length < 8) {
      this.error.set('Password must be at least 8 characters long');
      return;
    }

    this.loading.set(true);

    this.authenticationService.register(this.formData).pipe(
        tap(() => {
            this.router.navigate(['/auth/login']);
            this.loading.set(false);
        }),
        catchError((err) => {
            this.loading.set(false);
            return err;
        }),
        takeUntilDestroyed(this.destroyRef)
    ).subscribe();
  }
}
