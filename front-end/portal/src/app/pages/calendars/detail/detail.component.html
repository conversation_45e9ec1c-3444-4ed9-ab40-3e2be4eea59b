<div class="min-h-screen bg-gray-50 p-8">
    <div class="bg-white rounded-lg shadow-md p-8 w-full">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">
                @if (id === 'add') {
                    Create New Calendar
                } @else {
                    Manage Calendar
                }
            </h1>
            <p class="text-gray-600">
                @if (id === 'add') {
                    Set up a new booking calendar for your services
                } @else {
                    Update your booking calendar settings and availability
                }
            </p>
        </div>

        <!-- Error Message -->
        @if (error()) {
            <div class="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
                {{ error() }}
            </div>
        }

        <!-- Form -->
        <form [formGroup]="form" (ngSubmit)="submit()" class="space-y-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Left Column - Basic Information -->
                <div class="space-y-6">
                    <h3 class="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2">Basic Information</h3>
                    
                    <!-- Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                            Calendar Name *
                        </label>
                        <input
                                type="text"
                                id="name"
                                #nameInput
                                [formControl]="form.controls.name"
                                placeholder="e.g., 30-minute consultation"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        />
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                            Description
                        </label>
                        <textarea
                                id="description"
                                #descriptionInput
                                [formControl]="form.controls.description"
                                placeholder="Brief description of this calendar..."
                                rows="4"
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                        ></textarea>
                    </div>

                    <!-- Duration and Color Row -->
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                        <!-- Duration -->
                        <div>
                            <label for="duration" class="block text-sm font-medium text-gray-700 mb-2">
                                Duration (minutes) *
                            </label>
                            <input
                                    type="number"
                                    id="duration"
                                    #durationInput
                                    min="15"
                                    max="480"
                                    [formControl]="form.controls.duration"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            />
                        </div>

                        <!-- Color -->
                        <div>
                            <label for="color" class="block text-sm font-medium text-gray-700 mb-2">
                                Calendar Color *
                            </label>
                            <select
                                    id="color"
                                    #colorInput
                                    [formControl]="form.controls.color"
                                    class="w-full px-4 py-3 border border-gray-300 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            >
                                <option value="indigo">Indigo</option>
                                <option value="blue">Blue</option>
                                <option value="green">Green</option>
                                <option value="red">Red</option>
                                <option value="yellow">Yellow</option>
                                <option value="purple">Purple</option>
                            </select>
                        </div>
                    </div>

                    <!-- Features -->
                    <div>
                        <h4 class="text-md font-medium text-gray-900 mb-4">Features</h4>
                        <div class="space-y-4">
                            <!-- Conversion Tracking -->
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <label class="flex items-center justify-between">
                                    <div>
                                        <span class="text-sm font-medium text-gray-700">Enable Conversion Tracking</span>
                                        <p class="text-xs text-gray-500 mt-1">Track booking conversions and analytics</p>
                                    </div>
                                    <input
                                            type="checkbox"
                                            #conversionTrackingInput
                                            [formControl]="form.controls.conversion_tracking"
                                            class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                    />
                                </label>
                            </div>

                            <!-- Google Meet Integration -->
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <label class="flex items-center justify-between">
                                    <div>
                                        <span class="text-sm font-medium text-gray-700">Google Meet Integration</span>
                                        <p class="text-xs text-gray-500 mt-1">Automatically create Google Meet links for bookings</p>
                                    </div>
                                    <input
                                            type="checkbox"
                                            #googleMeetInput
                                            [formControl]="form.controls.google_meet_integration"
                                            class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                    />
                                </label>
                            </div>

                            <!-- What's Next Section -->
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <label class="flex items-center justify-between">
                                    <div>
                                        <span class="text-sm font-medium text-gray-700">Show "What's Next" Section</span>
                                        <p class="text-xs text-gray-500 mt-1">Display next steps after booking confirmation</p>
                                    </div>
                                    <input
                                            type="checkbox"
                                            #whatsNextInput
                                            [formControl]="form.controls.whats_next_section"
                                            class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                    />
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Right Column - Availability Schedule -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 border-b border-gray-200 pb-2 mb-4">Availability Schedule</h3>
                    <p class="text-sm text-gray-600 mb-4">Set when this calendar is available for bookings</p>
                    
                    <div class="space-y-2">
                        @for (day of daysOfWeek; track day.value) {
                            <div class="border border-gray-200 rounded-lg p-3">
                                <!-- Day header -->
                                <div class="flex items-center space-x-3 mb-2">
                                    <input 
                                        type="checkbox" 
                                        [id]="'day-' + day.value"
                                        [checked]="isDayEnabled(day.value)"
                                        (change)="$event.target.checked ? addScheduleForDay(day.value) : removeScheduleForDay(day.value)"
                                        class="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                                    />
                                    <label [for]="'day-' + day.value" class="text-sm font-medium text-gray-900">
                                        {{ day.label }}
                                    </label>
                                </div>
                                
                                <!-- Time slots for this day -->
                                @if (isDayEnabled(day.value)) {
                                    <div class="ml-7 space-y-2">
                                        @for (timeSlot of getScheduleForDay(day.value)?.time_slots; track $index) {
                                            <div class="flex items-center space-x-2">
                                                <input 
                                                    type="time"
                                                    [value]="timeSlot.start_time"
                                                    (change)="updateTimeSlot(day.value, $index, 'start_time', $event.target.value)"
                                                    class="px-2 py-1 border border-gray-300 rounded text-xs focus:ring-blue-500 focus:border-blue-500"
                                                />
                                                <span class="text-gray-500 text-xs">to</span>
                                                <input 
                                                    type="time"
                                                    [value]="timeSlot.end_time"
                                                    (change)="updateTimeSlot(day.value, $index, 'end_time', $event.target.value)"
                                                    class="px-2 py-1 border border-gray-300 rounded text-xs focus:ring-blue-500 focus:border-blue-500"
                                                />
                                                @if (getScheduleForDay(day.value)!.time_slots.length > 1) {
                                                    <button 
                                                        type="button"
                                                        (click)="removeTimeSlot(day.value, $index)"
                                                        class="text-red-500 hover:text-red-700 text-sm px-1 py-1 rounded"
                                                        title="Remove time slot"
                                                    >
                                                        ✕
                                                    </button>
                                                }
                                            </div>
                                        }
                                        <button 
                                            type="button"
                                            (click)="addTimeSlot(day.value)"
                                            class="text-blue-600 hover:text-blue-700 text-xs font-medium flex items-center space-x-1"
                                        >
                                            <span>+</span>
                                            <span>Add time slot</span>
                                        </button>
                                    </div>
                                }
                            </div>
                        }
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end space-x-4 pt-8 border-t border-gray-200">
                <button
                        type="button"
                        (click)="cancel()"
                        class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:border-transparent transition-colors font-medium"
                        [disabled]="submitting()"
                >
                    Cancel
                </button>

                <button
                        type="submit"
                        class="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors disabled:opacity-50 disabled:cursor-not-allowed font-medium"
                        [disabled]="submitting() || form.invalid"
                >
                    @if (submitting()) {
                        <span class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              @if (id === 'add') {
                Creating...
              } @else {
                Updating...
              }
            </span>
                    } @else {
                        @if (id === 'add') {
                            Create Calendar
                        } @else {
                            Update Calendar
                        }
                    }
                </button>
            </div>
        </form>
    </div>
</div>
