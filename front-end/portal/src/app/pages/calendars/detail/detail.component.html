<div class="min-h-screen bg-gradient-to-br from-orange-50 via-amber-50 to-yellow-50 p-4 sm:p-8">
    <div class="max-w-6xl mx-auto">
        <!-- Beautiful Header with Gradient Background -->
        <div class="relative overflow-hidden bg-gradient-to-r from-orange-500 to-amber-500 rounded-3xl shadow-2xl mb-8 p-8 sm:p-12">
            <!-- Decorative Background Elements -->
            <div class="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-32 translate-x-32"></div>
            <div class="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
            <div class="absolute top-1/2 left-1/2 w-32 h-32 bg-white/5 rounded-full -translate-x-1/2 -translate-y-1/2"></div>

            <div class="relative z-10">
                <div class="flex items-center space-x-4 mb-4">
                    <div class="w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center shadow-lg">
                        <i class="fas fa-calendar-plus text-white text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-4xl sm:text-5xl font-bold text-white mb-2 tracking-tight">
                            @if (id === 'add') {
                                Create New Calendar
                            } @else {
                                Manage Calendar
                            }
                        </h1>
                        <p class="text-white/90 text-xl font-medium">
                            @if (id === 'add') {
                                Set up a beautiful booking calendar for your services
                            } @else {
                                Update your booking calendar settings and availability
                            }
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Card -->
        <div class="bg-white/80 backdrop-blur-sm rounded-3xl shadow-2xl border border-white/20 overflow-hidden">
            <!-- Error Message -->
            @if (error()) {
                <div class="mx-8 mt-8 p-6 bg-red-50 border border-red-200 text-red-700 rounded-2xl flex items-center space-x-3 shadow-sm">
                    <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                        <i class="fas fa-exclamation-triangle text-red-500 text-sm"></i>
                    </div>
                    <span class="font-medium">{{ error() }}</span>
                </div>
            }

            <!-- Form -->
            <form [formGroup]="form" (ngSubmit)="submit()" class="p-8 space-y-10">
                <div class="grid grid-cols-1 xl:grid-cols-2 gap-12">
                    <!-- Left Column - Basic Information -->
                    <div class="space-y-8">
                        <!-- Section Header -->
                        <div class="flex items-center space-x-3 pb-4 mb-6">
                            <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-info-circle text-orange-500 text-sm"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-800">Basic Information</h3>
                                <p class="text-gray-600 text-sm">Configure your calendar details</p>
                            </div>
                        </div>

                        <!-- Name Field -->
                        <div class="group">
                            <label for="name" class="block text-sm font-bold text-gray-700 mb-3 group-focus-within:text-orange-600 transition-colors duration-200">
                                <i class="fas fa-tag mr-2 text-orange-500"></i>Calendar Name *
                            </label>
                            <div class="relative">
                                <input
                                    type="text"
                                    id="name"
                                    #nameInput
                                    [formControl]="form.controls.name"
                                    placeholder="e.g., 30-minute consultation"
                                    class="w-full px-6 py-4 border-2 border-gray-200 rounded-2xl text-gray-800 bg-white/70 backdrop-blur-sm focus:outline-none focus:ring-4 focus:ring-orange-200 focus:border-orange-400 transition-all duration-300 hover:border-orange-300 shadow-sm"
                                />
                                <div class="absolute inset-y-0 right-0 flex items-center pr-6">
                                    <i class="fas fa-calendar text-gray-400"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Description Field -->
                        <div class="group">
                            <label for="description" class="block text-sm font-bold text-gray-700 mb-3 group-focus-within:text-orange-600 transition-colors duration-200">
                                <i class="fas fa-align-left mr-2 text-orange-500"></i>Description
                            </label>
                            <textarea
                                id="description"
                                #descriptionInput
                                [formControl]="form.controls.description"
                                placeholder="Brief description of this calendar..."
                                rows="4"
                                class="w-full px-6 py-4 border-2 border-gray-200 rounded-2xl text-gray-800 bg-white/70 backdrop-blur-sm focus:outline-none focus:ring-4 focus:ring-orange-200 focus:border-orange-400 resize-none transition-all duration-300 hover:border-orange-300 shadow-sm"
                            ></textarea>
                        </div>

                        <!-- Duration and Color Row -->
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                            <!-- Duration Field -->
                            <div class="group">
                                <label for="duration" class="block text-sm font-bold text-gray-700 mb-3 group-focus-within:text-orange-600 transition-colors duration-200">
                                    <i class="fas fa-clock mr-2 text-orange-500"></i>Duration (minutes) *
                                </label>
                                <div class="relative">
                                    <input
                                        type="number"
                                        id="duration"
                                        #durationInput
                                        min="15"
                                        max="480"
                                        [formControl]="form.controls.duration"
                                        class="w-full px-6 py-4 border-2 border-gray-200 rounded-2xl text-gray-800 bg-white/70 backdrop-blur-sm focus:outline-none focus:ring-4 focus:ring-orange-200 focus:border-orange-400 transition-all duration-300 hover:border-orange-300 shadow-sm"
                                    />
                                    <div class="absolute inset-y-0 right-0 flex items-center pr-6">
                                        <span class="text-gray-400 text-sm font-medium">min</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Color Field -->
                            <div class="group">
                                <label for="color" class="block text-sm font-bold text-gray-700 mb-3 group-focus-within:text-orange-600 transition-colors duration-200">
                                    <i class="fas fa-palette mr-2 text-orange-500"></i>Calendar Color *
                                </label>
                                <div class="relative">
                                    <select
                                        id="color"
                                        #colorInput
                                        [formControl]="form.controls.color"
                                        class="w-full px-6 py-4 border-2 border-gray-200 rounded-2xl text-gray-800 bg-white/70 backdrop-blur-sm focus:outline-none focus:ring-4 focus:ring-orange-200 focus:border-orange-400 transition-all duration-300 hover:border-orange-300 shadow-sm appearance-none"
                                    >
                                        <option value="indigo">🟣 Indigo</option>
                                        <option value="blue">🔵 Blue</option>
                                        <option value="green">🟢 Green</option>
                                        <option value="red">🔴 Red</option>
                                        <option value="yellow">🟡 Yellow</option>
                                        <option value="purple">🟪 Purple</option>
                                    </select>
                                    <div class="absolute inset-y-0 right-0 flex items-center pr-6 pointer-events-none">
                                        <i class="fas fa-chevron-down text-gray-400"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Features Section -->
                        <div>
                            <div class="flex items-center space-x-3 mb-6">
                                <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-magic text-purple-500 text-sm"></i>
                                </div>
                                <div>
                                    <h4 class="text-xl font-bold text-gray-800">Advanced Features</h4>
                                    <p class="text-gray-600 text-sm">Enhance your calendar capabilities</p>
                                </div>
                            </div>

                            <div class="space-y-6">
                                <!-- Conversion Tracking -->
                                <div class="bg-white border border-gray-200 rounded-xl p-4 hover:shadow-md transition-all duration-200">
                                    <label class="flex items-center justify-between cursor-pointer">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                                                <i class="fas fa-chart-line text-orange-500 text-sm"></i>
                                            </div>
                                            <div>
                                                <span class="text-base font-semibold text-gray-800 block">Enable Conversion Tracking</span>
                                                <p class="text-sm text-gray-600">Track booking conversions and detailed analytics</p>
                                            </div>
                                        </div>
                                        <div class="relative">
                                            <input
                                                type="checkbox"
                                                #conversionTrackingInput
                                                [formControl]="form.controls.conversion_tracking"
                                                class="sr-only peer"
                                            />
                                            <div class="w-12 h-6 bg-gray-200 rounded-full peer peer-checked:bg-orange-400 transition-colors duration-300"></div>
                                            <div class="absolute left-1 top-1 w-4 h-4 bg-white rounded-full transition-transform duration-300 peer-checked:translate-x-6 shadow-sm"></div>
                                        </div>
                                    </label>
                                </div>

                                <!-- Google Meet Integration -->
                                <div class="bg-white border border-gray-200 rounded-xl p-4 hover:shadow-md transition-all duration-200">
                                    <label class="flex items-center justify-between cursor-pointer">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                                <i class="fab fa-google text-blue-500 text-sm"></i>
                                            </div>
                                            <div>
                                                <span class="text-base font-semibold text-gray-800 block">Google Meet Integration</span>
                                                <p class="text-sm text-gray-600">Automatically create Google Meet links for bookings</p>
                                            </div>
                                        </div>
                                        <div class="relative">
                                            <input
                                                type="checkbox"
                                                #googleMeetInput
                                                [formControl]="form.controls.google_meet_integration"
                                                class="sr-only peer"
                                            />
                                            <div class="w-12 h-6 bg-gray-200 rounded-full peer peer-checked:bg-blue-400 transition-colors duration-300"></div>
                                            <div class="absolute left-1 top-1 w-4 h-4 bg-white rounded-full transition-transform duration-300 peer-checked:translate-x-6 shadow-sm"></div>
                                        </div>
                                    </label>
                                </div>

                                <!-- What's Next Section -->
                                <div class="bg-white border border-gray-200 rounded-xl p-4 hover:shadow-md transition-all duration-200">
                                    <label class="flex items-center justify-between cursor-pointer">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                                <i class="fas fa-arrow-right text-green-500 text-sm"></i>
                                            </div>
                                            <div>
                                                <span class="text-base font-semibold text-gray-800 block">Show "What's Next" Section</span>
                                                <p class="text-sm text-gray-600">Display helpful next steps after booking confirmation</p>
                                            </div>
                                        </div>
                                        <div class="relative">
                                            <input
                                                type="checkbox"
                                                #whatsNextInput
                                                [formControl]="form.controls.whats_next_section"
                                                class="sr-only peer"
                                            />
                                            <div class="w-12 h-6 bg-gray-200 rounded-full peer peer-checked:bg-green-400 transition-colors duration-300"></div>
                                            <div class="absolute left-1 top-1 w-4 h-4 bg-white rounded-full transition-transform duration-300 peer-checked:translate-x-6 shadow-sm"></div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>
                </div>

                    <!-- Right Column - Availability Schedule -->
                    <div class="space-y-8">
                        <!-- Section Header -->
                        <div class="flex items-center space-x-3 pb-4 mb-6">
                            <div class="w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-calendar-week text-teal-500 text-sm"></i>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-800">Availability Schedule</h3>
                                <p class="text-gray-600 text-sm">Set when this calendar is available for bookings</p>
                            </div>
                        </div>

                        <!-- Weekly Hours Section - Calendly Style -->
                        <div class="bg-white/70 backdrop-blur-sm border-2 border-gray-200 rounded-3xl p-8 shadow-xl">
                            <div class="flex items-center justify-between mb-6">
                                <div class="flex items-center space-x-3">
                                    <div class="w-8 h-8 bg-teal-100 rounded-full flex items-center justify-center">
                                        <i class="fas fa-clock text-teal-500 text-sm"></i>
                                    </div>
                                    <h4 class="text-lg font-bold text-gray-800">Weekly hours</h4>
                                </div>
                                <p class="text-sm text-gray-600">Set when you are typically available for meetings</p>
                            </div>

                            <!-- Days List -->
                            <div class="space-y-6">
                                @for (day of daysOfWeek; track day.value) {
                                    <div class="flex items-center space-x-6 py-3 hover:bg-orange-50/50 rounded-2xl px-4 transition-all duration-200">
                                        <!-- Day Circle -->
                                        <div class="flex items-center space-x-6 min-w-[140px]">
                                            <button
                                                type="button"
                                                (click)="isDayEnabled(day.value) ? removeScheduleForDay(day.value) : addScheduleForDay(day.value)"
                                                class="w-10 h-10 rounded-full border-3 flex items-center justify-center text-sm font-bold transition-all duration-300 shadow-md hover:shadow-lg"
                                                [class.bg-gradient-to-r]="isDayEnabled(day.value)"
                                                [class.from-orange-400]="isDayEnabled(day.value)"
                                                [class.to-amber-400]="isDayEnabled(day.value)"
                                                [class.border-orange-400]="isDayEnabled(day.value)"
                                                [class.text-white]="isDayEnabled(day.value)"
                                                [class.border-gray-300]="!isDayEnabled(day.value)"
                                                [class.text-gray-500]="!isDayEnabled(day.value)"
                                                [class.hover:border-orange-400]="!isDayEnabled(day.value)"
                                                [class.hover:text-orange-600]="!isDayEnabled(day.value)"
                                                [class.bg-white]="!isDayEnabled(day.value)"
                                            >
                                                {{ day.label.charAt(0) }}
                                            </button>
                                            <span class="text-base font-semibold text-gray-700 min-w-[90px]">
                                                {{ day.label }}
                                            </span>
                                        </div>

                                        <!-- Time Slots or Unavailable -->
                                        <div class="flex-1">
                                            @if (isDayEnabled(day.value)) {
                                                <div class="space-y-3">
                                                    @for (timeSlot of getScheduleForDay(day.value)?.time_slots; track $index) {
                                                        <div class="flex items-center space-x-4 bg-white/80 rounded-xl p-3 shadow-sm border border-gray-200">
                                                            <input
                                                                type="time"
                                                                [value]="timeSlot.start_time"
                                                                (change)="updateTimeSlot(day.value, $index, 'start_time', $event.target.value)"
                                                                class="px-4 py-2 border-2 border-gray-200 rounded-xl text-sm font-medium focus:ring-4 focus:ring-orange-200 focus:border-orange-400 bg-white transition-all duration-200 w-28"
                                                            />
                                                            <span class="text-gray-500 text-sm font-bold">-</span>
                                                            <input
                                                                type="time"
                                                                [value]="timeSlot.end_time"
                                                                (change)="updateTimeSlot(day.value, $index, 'end_time', $event.target.value)"
                                                                class="px-4 py-2 border-2 border-gray-200 rounded-xl text-sm font-medium focus:ring-4 focus:ring-orange-200 focus:border-orange-400 bg-white transition-all duration-200 w-28"
                                                            />
                                                            @if ((getScheduleForDay(day.value)?.time_slots?.length || 0) > 1) {
                                                                <button
                                                                    type="button"
                                                                    (click)="removeTimeSlot(day.value, $index)"
                                                                    class="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-lg transition-all duration-200"
                                                                    title="Remove time slot"
                                                                >
                                                                    <i class="fas fa-times text-sm"></i>
                                                                </button>
                                                            }
                                                            @if ($index === (getScheduleForDay(day.value)?.time_slots?.length || 0) - 1) {
                                                                <button
                                                                    type="button"
                                                                    (click)="addTimeSlot(day.value)"
                                                                    class="w-8 h-8 flex items-center justify-center text-gray-400 hover:text-orange-500 hover:bg-orange-50 rounded-lg transition-all duration-200"
                                                                    title="Add time slot"
                                                                >
                                                                    <i class="fas fa-plus text-sm"></i>
                                                                </button>
                                                            }
                                                        </div>
                                                    }
                                                </div>
                                            } @else {
                                                <div class="flex items-center space-x-3 text-gray-500 bg-gray-50 rounded-xl px-4 py-3">
                                                    <i class="fas fa-minus-circle text-lg"></i>
                                                    <span class="text-base font-medium">Unavailable</span>
                                                </div>
                                            }
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
            </div>

                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row justify-end space-y-4 sm:space-y-0 sm:space-x-6 pt-10 border-t-2 border-orange-100">
                    <button
                        type="button"
                        (click)="cancel()"
                        class="px-8 py-4 border-2 border-gray-300 text-gray-700 rounded-2xl hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-4 focus:ring-gray-200 transition-all duration-300 font-bold text-lg shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
                        [disabled]="submitting()"
                    >
                        <i class="fas fa-times mr-2"></i>Cancel
                    </button>

                    <button
                        type="submit"
                        class="px-10 py-4 bg-gradient-to-r from-orange-500 to-amber-500 text-white rounded-2xl hover:from-orange-600 hover:to-amber-600 focus:outline-none focus:ring-4 focus:ring-orange-200 focus:ring-offset-2 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed font-bold text-lg shadow-xl hover:shadow-2xl relative overflow-hidden group"
                        [disabled]="submitting() || form.invalid"
                    >
                        <!-- Button background animation -->
                        <div class="absolute inset-0 bg-gradient-to-r from-amber-400 to-orange-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                        <span class="relative z-10 flex items-center justify-center">
                            @if (submitting()) {
                                <svg class="animate-spin -ml-1 mr-3 h-6 w-6 text-white" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                @if (id === 'add') {
                                    Creating...
                                } @else {
                                    Updating...
                                }
                            } @else {
                                <i class="fas fa-save mr-2"></i>
                                @if (id === 'add') {
                                    Create Calendar
                                } @else {
                                    Update Calendar
                                }
                            }
                        </span>
                    </button>
                </div>
        </form>
    </div>
</div>
