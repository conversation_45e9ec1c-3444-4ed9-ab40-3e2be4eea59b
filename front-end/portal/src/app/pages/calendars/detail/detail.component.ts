import {Component, DestroyRef, inject, Input, OnInit, signal} from '@angular/core';
import {FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators} from "@angular/forms";
import {CalendarService} from "@api/calendar/calendar.service";
import {Router} from "@angular/router";
import {BookingCalendar, CreateBookingCalendarRequest, CreateScheduleRequest, CreateTimeSlotRequest} from "@api/calendar/calendar.interface";
import {catchError, tap} from "rxjs";
import {takeUntilDestroyed} from "@angular/core/rxjs-interop";

interface Form {
    name: FormControl<string|null>;
    description: FormControl<string|null>;
    duration: FormControl<number|null>;
    color: FormControl<string|null>;
    conversion_tracking: FormControl<boolean>;
    google_meet_integration: FormControl<boolean>;
    whats_next_section: FormControl<boolean>;
}

interface DayOfWeek {
    value: string;
    label: string;
}

@Component({
  selector: 'app-detail',
    imports: [
        FormsModule,
        ReactiveFormsModule
    ],
  templateUrl: './detail.component.html',
})
export class DetailComponent implements OnInit {
    @Input({alias: 'id'}) id!: string;
    public submitting = signal(false);
    public error = signal<string | null>(null);
    public loading = signal<boolean>(false);
    public calendar = signal<BookingCalendar|null>(null);

    // Form state
    public form: FormGroup<Form> = new FormGroup<Form>({
        name: new FormControl(null, [Validators.required]),
        description: new FormControl(null),
        duration: new FormControl(null, [Validators.required]),
        color: new FormControl(null, [Validators.required]),
        conversion_tracking: new FormControl(false, {nonNullable: true}),
        google_meet_integration: new FormControl(false, {nonNullable: true}),
        whats_next_section: new FormControl(false, {nonNullable: true}),
    });

    // Schedule state
    public schedules = signal<CreateScheduleRequest[]>([]);
    
    public daysOfWeek: DayOfWeek[] = [
        { value: 'monday', label: 'Monday' },
        { value: 'tuesday', label: 'Tuesday' },
        { value: 'wednesday', label: 'Wednesday' },
        { value: 'thursday', label: 'Thursday' },
        { value: 'friday', label: 'Friday' },
        { value: 'saturday', label: 'Saturday' },
        { value: 'sunday', label: 'Sunday' }
    ];

    private calendarService = inject(CalendarService);
    private router = inject(Router);
    private destroyRef = inject(DestroyRef);

    public ngOnInit(): void {
        this.loadCalendar();
    }

    // Schedule management methods
    isDayEnabled(dayOfWeek: string): boolean {
        return this.schedules().some(schedule => schedule.day_of_week === dayOfWeek);
    }

    getScheduleForDay(dayOfWeek: string): CreateScheduleRequest | undefined {
        return this.schedules().find(schedule => schedule.day_of_week === dayOfWeek);
    }

    addScheduleForDay(dayOfWeek: string): void {
        const currentSchedules = this.schedules();
        if (!this.isDayEnabled(dayOfWeek)) {
            const newSchedule: CreateScheduleRequest = {
                day_of_week: dayOfWeek,
                time_slots: [{ start_time: '09:00', end_time: '17:00' }]
            };
            this.schedules.set([...currentSchedules, newSchedule]);
        }
    }

    removeScheduleForDay(dayOfWeek: string): void {
        const currentSchedules = this.schedules();
        this.schedules.set(currentSchedules.filter(schedule => schedule.day_of_week !== dayOfWeek));
    }

    addTimeSlot(dayOfWeek: string): void {
        const currentSchedules = this.schedules();
        const updatedSchedules = currentSchedules.map(schedule => {
            if (schedule.day_of_week === dayOfWeek) {
                return {
                    ...schedule,
                    time_slots: [...schedule.time_slots, { start_time: '09:00', end_time: '17:00' }]
                };
            }
            return schedule;
        });
        this.schedules.set(updatedSchedules);
    }

    removeTimeSlot(dayOfWeek: string, timeSlotIndex: number): void {
        const currentSchedules = this.schedules();
        const updatedSchedules = currentSchedules.map(schedule => {
            if (schedule.day_of_week === dayOfWeek) {
                const newTimeSlots = schedule.time_slots.filter((_: CreateTimeSlotRequest, index: number) => index !== timeSlotIndex);
                return {
                    ...schedule,
                    time_slots: newTimeSlots
                };
            }
            return schedule;
        });
        this.schedules.set(updatedSchedules);
    }

    updateTimeSlot(dayOfWeek: string, timeSlotIndex: number, field: 'start_time' | 'end_time', value: string): void {
        const currentSchedules = this.schedules();
        const updatedSchedules = currentSchedules.map(schedule => {
            if (schedule.day_of_week === dayOfWeek) {
                const updatedTimeSlots = schedule.time_slots.map((timeSlot: CreateTimeSlotRequest, index: number) => {
                    if (index === timeSlotIndex) {
                        return { ...timeSlot, [field]: value };
                    }
                    return timeSlot;
                });
                return {
                    ...schedule,
                    time_slots: updatedTimeSlots
                };
            }
            return schedule;
        });
        this.schedules.set(updatedSchedules);
    }

    // Create calendar
    submit() {
        const formData = this.form.value as CreateBookingCalendarRequest;
        
        // Include schedules in the request
        const form: CreateBookingCalendarRequest = {
            ...formData,
            schedules: this.schedules()
        };

        this.submitting.set(true);
        this.error.set(null);

        let service = this.calendarService.store(form);

        const calendar = this.calendar();

        if(calendar) {
            service = this.calendarService.update(calendar, form);
        }

        service.pipe(
            tap((calendar) => {
                this.router.navigate(['/calendars']);
            }),
            catchError((err) => {
                this.error.set('Failed to create calendar. Please try again.');
                this.submitting.set(false);
                return err;
            }),
            takeUntilDestroyed(this.destroyRef)
        ).subscribe();
    }

    // Cancel and go back
    cancel() {
        this.router.navigate(['/calendars']);
    }

    // Format time string for HTML time input (ensure HH:MM format)
    private formatTimeForInput(timeString: string): string {
        if (!timeString) return '';
        
        // If it's a full datetime string (like '2025-09-11T10:00:00.000000Z'), extract time
        if (timeString.includes('T')) {
            const timePart = timeString.split('T')[1];
            if (timePart) {
                // Extract HH:MM from HH:MM:SS.microseconds
                return timePart.substring(0, 5);
            }
        }
        
        // If it's already in HH:MM format, return as is
        if (/^\d{2}:\d{2}$/.test(timeString)) {
            return timeString;
        }
        
        // If it's in HH:MM:SS format, strip the seconds
        if (/^\d{2}:\d{2}:\d{2}$/.test(timeString)) {
            return timeString.substring(0, 5);
        }
        
        // If it's in H:MM format, pad with zero
        if (/^\d{1}:\d{2}$/.test(timeString)) {
            return '0' + timeString;
        }
        
        return timeString;
    }

    private loadCalendar(): void {
        if (this.id === 'add') {
            return
        }

        if (this.loading()) {
            return;
        }

        this.loading.set(true);

        this.calendarService.show(parseInt(this.id)).pipe(
            tap((calendar) => {
                this.calendar.set(calendar);
                this.form.patchValue(calendar);
                
                // Populate schedules if they exist
                if (calendar.schedules && calendar.schedules.length > 0) {
                    const schedules: CreateScheduleRequest[] = calendar.schedules.map(schedule => ({
                        day_of_week: schedule.day_of_week,
                        time_slots: schedule.time_slots.map(timeSlot => ({
                            start_time: this.formatTimeForInput(timeSlot.start_time),
                            end_time: this.formatTimeForInput(timeSlot.end_time)
                        }))
                    }));
                    this.schedules.set(schedules);
                }
                
                this.loading.set(false);
            }),
            catchError((err) => {
                this.loading.set(false);
                return err;
            }),
            takeUntilDestroyed(this.destroyRef)
        ).subscribe()
    }
}
