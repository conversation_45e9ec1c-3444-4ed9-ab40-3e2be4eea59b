

  <!-- Main Content Area -->
  <div class="flex-1 flex flex-col">
    <!-- Top Header -->
    <header class="bg-white border-b border-gray-200 px-6 py-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div class="flex items-center space-x-2">
            <i class="fas fa-chart-bar text-xl"></i>
            <div>
              <h1 class="text-2xl font-bold text-gray-900">Dashboard</h1>
              <p class="text-gray-600 text-sm">Manage your conversion-optimized booking calendars and track everything</p>
            </div>
          </div>
        </div>
        
        <div class="flex items-center space-x-3">
          <button 
            (click)="livePreview()"
            class="flex items-center space-x-2 px-3 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors text-sm"
          >
            <i class="fas fa-link"></i>
            <span>Live Preview</span>
          </button>
          
          <button 
            (click)="createCalendar()"
            class="flex items-center space-x-2 px-3 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-sm"
          >
            <i class="fas fa-plus"></i>
            <span>Create Calendar</span>
          </button>

          <button 
            (click)="onLogout()"
            class="flex items-center space-x-2 px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm"
          >
            <i class="fas fa-sign-out-alt"></i>
            <span>Logout</span>
          </button>
        </div>
      </div>
    </header>

    <!-- Dashboard Content -->
    <main class="flex-1 p-6 bg-gray-50">
      @if (loading()) {
        <div class="flex items-center justify-center h-64">
          <div class="text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
            <p class="text-gray-500">Loading calendars...</p>
          </div>
        </div>
      } @else {
        <!-- Calendars Section -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          @if (calendars().length === 0) {
            <!-- Empty State -->
            <div class="text-center py-16">
              <div class="w-20 h-20 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <i class="fas fa-calendar text-3xl text-gray-500"></i>
              </div>
              <h3 class="text-xl font-medium text-gray-900 mb-3">No calendars yet</h3>
              <p class="text-gray-600 mb-8 max-w-md mx-auto">
                Create your first conversion-optimized booking calendar to start tracking everything
              </p>
              <a
                routerLink="add"
                class="inline-flex items-center space-x-2 px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors text-sm"
              >
                <i class="fas fa-plus"></i>
                <span>Submit</span>
              </a>
            </div>
          } @else {
            <!-- Calendars List -->
            <div class="space-y-4">
              @for (calendar of calendars(); track calendar.id) {
                <div class="bg-white border border-gray-200 rounded-lg p-4">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                      <!-- Calendar Color Indicator -->
                      <div 
                        class="w-3 h-3 rounded-full flex-shrink-0"
                        [style.background-color]="calendar.color"
                      ></div>
                      
                      <!-- Calendar Info -->
                      <div class="flex-1">
                        <h3 class="font-medium text-gray-900">{{ calendar.name }}</h3>
                        <p class="text-sm text-gray-500" *ngIf="calendar.description">{{ calendar.description }}</p>
                        <div class="flex items-center space-x-4 mt-1">
                          <span class="text-xs text-gray-500">
                            <i class="far fa-clock mr-1"></i>{{ calendar.duration }} min
                          </span>
                          <span class="inline-flex items-center px-2 py-1 rounded-full text-xs" 
                                [class]="calendar.is_active ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'">
                            {{ calendar.is_active ? 'Active' : 'Inactive' }}
                          </span>
                          @if (calendar.conversion_tracking) {
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800">
                              <i class="fas fa-chart-line mr-1"></i>Tracking
                            </span>
                          }
                        </div>
                      </div>
                    </div>
                    
                    <!-- Calendar Actions -->
                    <div class="flex items-center space-x-2">
                      <button 
                        (click)="copyCalendarLink(calendar)"
                        class="flex items-center space-x-1 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors text-sm"
                        title="Copy Link"
                      >
                        <i class="fas fa-link"></i>
                        <span>Copy Link</span>
                      </button>
                      
                      <button 
                        (click)="viewCalendar(calendar)"
                        class="flex items-center space-x-1 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors text-sm"
                        title="View"
                      >
                        <i class="fas fa-external-link-alt"></i>
                        <span>View</span>
                      </button>
                      
                      <a
                        [routerLink]="['/calendars', calendar.id]"
                        class="flex items-center space-x-1 px-3 py-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors text-sm"
                        title="Manage"
                      >
                        <i class="fas fa-cog"></i>
                        <span>Manage</span>
                      </a>
                      
                      <button 
                        (click)="deleteCalendar(calendar)"
                        class="flex items-center space-x-1 px-3 py-2 text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors text-sm"
                        title="Delete"
                      >
                        <i class="fas fa-trash"></i>
                        <span>Delete</span>
                      </button>
                    </div>
                  </div>
                </div>
              }
            </div>
          }
        </div>
      }
    </main>
  </div>

<!-- Create Calendar Modal -->
@if (showCreateCalendarModal()) {
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white rounded-xl w-full h-full max-w-7xl max-h-[95vh] overflow-y-auto flex flex-col">
      <!-- Header -->
      <div class="shrink-0 bg-white border-b border-gray-200 px-8 py-6 rounded-t-xl">
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-2xl font-bold text-gray-900">Create New Calendar</h2>
            <p class="text-gray-600 mt-1">Set up a conversion-optimized booking calendar that tracks everything.</p>
          </div>
          <button 
            (click)="closeCreateModal()" 
            class="text-gray-400 hover:text-gray-600 transition-colors text-2xl"
          >
            ✕
          </button>
        </div>
      </div>
      
      <!-- Content -->
      <div class="flex-1 overflow-y-auto px-8 py-6">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          <!-- Left Column - Basic Information -->
          <div class="space-y-6">
            <div>
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
              
              <!-- Calendar Name -->
              <div class="mb-4">
                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                  Calendar Name *
                </label>
                <div class="relative">
                  <input 
                    type="text" 
                    id="name" 
                    #nameInput
                    [(ngModel)]="calendarForm().name"
                    (input)="updateCalendarForm('name', nameInput.value)"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    placeholder="e.g., Strategy Session - 30min"
                  />
                  <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                    <span class="text-blue-500">ⓘ</span>
                  </div>
                </div>
              </div>
              
              <!-- Description -->
              <div class="mb-4">
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                  Description (Optional)
                </label>
                <textarea 
                  id="description" 
                  #descriptionInput
                  [(ngModel)]="calendarForm().description"
                  (input)="updateCalendarForm('description', descriptionInput.value)"
                  rows="3"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent resize-none"
                  placeholder="What will visitors get from this booking? (helps with conversion)"
                ></textarea>
              </div>
              
              <!-- Duration -->
              <div class="mb-4">
                <label for="duration" class="block text-sm font-medium text-gray-700 mb-2">
                  Duration (minutes) *
                </label>
                <input 
                  type="number" 
                  id="duration" 
                  #durationInput
                  min="15"
                  max="480"
                  [(ngModel)]="calendarForm().duration"
                  (input)="updateCalendarForm('duration', +durationInput.value)"
                  class="w-full px-4 py-3 border border-gray-300 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="30"
                />
              </div>
              
              <!-- Color -->
              <div class="mb-4">
                <label for="color" class="block text-sm font-medium text-gray-700 mb-2">
                  Color *
                </label>
                <div class="relative">
                  <select 
                    id="color"
                    #colorSelect
                    [(ngModel)]="calendarForm().color"
                    (change)="updateCalendarForm('color', colorSelect.value)"
                    class="w-full px-4 py-3 border border-gray-300 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent appearance-none"
                  >
                    @for (color of availableColors; track color.value) {
                      <option [value]="color.value">{{ color.name }}</option>
                    }
                  </select>
                  <div class="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                    <div 
                      class="w-4 h-4 rounded-full border border-gray-300"
                      [style.background-color]="calendarForm().color"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Features -->
            <div>
              <h3 class="text-lg font-semibold text-gray-900 mb-4">Features</h3>
              
              <!-- Conversion Tracking -->
              <div class="bg-gray-50 p-4 rounded-lg mb-4">
                <div class="flex items-center justify-between">
                  <div>
                    <h4 class="font-medium text-gray-900">Conversion Tracking</h4>
                    <p class="text-sm text-gray-600 mt-1">Track all interactions, clicks, and conversions for this calendar</p>
                  </div>
                  <label class="relative inline-flex items-center cursor-pointer">
                    <input 
                      type="checkbox" 
                      [(ngModel)]="calendarForm().conversion_tracking"
                      (change)="updateCalendarForm('conversion_tracking', $event.target.checked)"
                      class="sr-only peer"
                    >
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-orange-600"></div>
                  </label>
                </div>
              </div>
              
              <!-- Google Meet Integration -->
              <div class="bg-gray-50 p-4 rounded-lg mb-4">
                <div class="flex items-center justify-between">
                  <div>
                    <h4 class="font-medium text-gray-900">Google Meet Integration</h4>
                    <p class="text-sm text-gray-600 mt-1">Enable Google Meet links for this calendar</p>
                  </div>
                  <label class="relative inline-flex items-center cursor-pointer">
                    <input 
                      type="checkbox" 
                      [(ngModel)]="calendarForm().google_meet_integration"
                      (change)="updateCalendarForm('google_meet_integration', $event.target.checked)"
                      class="sr-only peer"
                    >
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gray-400"></div>
                  </label>
                </div>
              </div>
              
              <!-- "What's Next" Section -->
              <div class="bg-gray-50 p-4 rounded-lg">
                <div class="flex items-center justify-between">
                  <div>
                    <h4 class="font-medium text-gray-900">"What's Next" Section</h4>
                    <p class="text-sm text-gray-600 mt-1">Show visitors what happens after they book</p>
                  </div>
                  <label class="relative inline-flex items-center cursor-pointer">
                    <input 
                      type="checkbox" 
                      [(ngModel)]="calendarForm().whats_next_section"
                      (change)="updateCalendarForm('whats_next_section', $event.target.checked)"
                      class="sr-only peer"
                    >
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-gray-400"></div>
                  </label>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Right Column - Availability Schedule -->
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-4">Availability Schedule</h3>
            <p class="text-gray-600 mb-6">Set when this calendar is available for bookings</p>
            
            <div class="space-y-4">
              @for (day of daysOfWeek; track day.value) {
                <div class="border border-gray-200 rounded-lg p-4">
                  <!-- Day header -->
                  <div class="flex items-center space-x-3 mb-3">
                    <input 
                      type="checkbox" 
                      [id]="'day-' + day.value"
                      [checked]="isDayEnabled(day.value)"
                      (change)="$event.target.checked ? addScheduleForDay(day.value) : removeScheduleForDay(day.value)"
                      class="w-5 h-5 text-orange-600 border-gray-300 rounded focus:ring-orange-500"
                    />
                    <label [for]="'day-' + day.value" class="text-lg font-medium text-gray-900">
                      {{ day.label }}
                    </label>
                  </div>
                  
                  <!-- Time slots for this day -->
                  @if (isDayEnabled(day.value)) {
                    <div class="ml-8 space-y-3">
                      @for (timeSlot of getScheduleForDay(day.value)?.time_slots; track $index) {
                        <div class="flex items-center space-x-3">
                          <input 
                            type="time"
                            [value]="timeSlot.start_time"
                            (change)="updateTimeSlot(day.value, $index, 'start_time', $event.target.value)"
                            class="px-3 py-2 border border-gray-300 rounded text-sm focus:ring-orange-500 focus:border-orange-500"
                          />
                          <span class="text-gray-500 font-medium">to</span>
                          <input 
                            type="time"
                            [value]="timeSlot.end_time"
                            (change)="updateTimeSlot(day.value, $index, 'end_time', $event.target.value)"
                            class="px-3 py-2 border border-gray-300 rounded text-sm focus:ring-orange-500 focus:border-orange-500"
                          />
                          @if (getScheduleForDay(day.value)!.time_slots.length > 1) {
                            <button 
                              type="button"
                              (click)="removeTimeSlot(day.value, $index)"
                              class="text-red-500 hover:text-red-700 text-lg font-bold"
                              title="Remove time slot"
                            >
                              ✕
                            </button>
                          }
                        </div>
                      }
                      <button 
                        type="button"
                        (click)="addTimeSlot(day.value)"
                        class="text-orange-600 hover:text-orange-700 text-sm font-medium flex items-center space-x-1"
                      >
                        <span>+</span>
                        <span>Add time slot</span>
                      </button>
                    </div>
                  }
                </div>
              }
            </div>
          </div>
        </div>
      </div>
      
      <!-- Footer -->
      <div class="shrink-0 bg-white border-t border-gray-200 px-8 py-6 rounded-b-xl">
        <div class="flex items-center justify-end space-x-4 max-w-6xl mx-auto">
          <button 
            type="button" 
            (click)="closeCreateModal()"
            class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
          >
            Cancel
          </button>
          <button 
            type="button"
            (click)="onCreateCalendar()"
            [disabled]="!calendarForm().name.trim() || loading()"
            class="px-8 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed font-medium"
          >
            @if (loading()) {
              <span class="inline-flex items-center">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creating...
              </span>
            } @else {
              Create Calendar
            }
          </button>
        </div>
      </div>
    </div>
  </div>
}
