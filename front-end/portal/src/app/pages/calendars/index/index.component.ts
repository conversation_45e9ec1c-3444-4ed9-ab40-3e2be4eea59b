import { Component, signal, inject, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { AuthState } from '@states/auth.state';
import { CalendarService } from '@api/calendar/calendar.service';
import { BookingCalendar, CreateBookingCalendarRequest, CreateScheduleRequest } from '@api/calendar/calendar.interface';

@Component({
  selector: 'app-index',
  standalone: true,
  imports: [CommonModule, RouterModule, FormsModule],
  templateUrl: './index.component.html',
  styleUrl: './index.component.scss'
})
export class IndexComponent implements OnInit {
  private authState = inject(AuthState);
  private router = inject(Router);
  private calendarService = inject(CalendarService);
  
  readonly user = this.authState.user;
  readonly loading = signal(false);

  // Calendar management
  calendars = signal<BookingCalendar[]>([]);
  showCreateCalendarModal = signal(false);

  // Form data for creating calendars
  calendarForm = signal<CreateBookingCalendarRequest>({
    name: '',
    description: '',
    duration: 30,
    color: '#6366f1',
    conversion_tracking: true,
    google_meet_integration: false,
    whats_next_section: false,
    schedules: []
  });

  // Days of the week
  daysOfWeek = [
    { value: 'monday', label: 'Monday' },
    { value: 'tuesday', label: 'Tuesday' },
    { value: 'wednesday', label: 'Wednesday' },
    { value: 'thursday', label: 'Thursday' },
    { value: 'friday', label: 'Friday' },
    { value: 'saturday', label: 'Saturday' },
    { value: 'sunday', label: 'Sunday' }
  ];

  // Available colors for calendar
  availableColors = [
    { name: 'Indigo', value: '#6366f1' },
    { name: 'Blue', value: '#3b82f6' },
    { name: 'Green', value: '#10b981' },
    { name: 'Orange', value: '#f97316' },
    { name: 'Red', value: '#ef4444' },
    { name: 'Purple', value: '#8b5cf6' },
    { name: 'Pink', value: '#ec4899' },
    { name: 'Gray', value: '#6b7280' }
  ];

    public navigationItems = [
        { icon: 'fas fa-user', label: 'Account', route: '/account', active: false },
        { icon: 'fas fa-calendar', label: 'Calendars', route: '/index', active: true },
        { icon: 'fas fa-cog', label: 'Account Settings', route: '/settings', active: false },
        { icon: 'fas fa-tools', label: 'Tools', route: '/tools', active: false },
        { icon: 'fas fa-chart-bar', label: 'Analytics', route: '/analytics', active: false },
        { icon: 'fas fa-handshake', label: 'Meetings', route: '/meetings', active: false },
        { icon: 'fas fa-users', label: 'Contacts', route: '/contacts', active: false },
        { icon: 'fas fa-clock', label: 'Availability', route: '/availability', active: false },
        { icon: 'fas fa-calendar', label: 'Google Calendar', route: '/google-calendar', active: false },
        { icon: 'fab fa-apple', label: 'Apple Calendar', route: '/apple-calendar', active: false },
    ];

  ngOnInit() {
    // Load user data when index loads
    this.authState.loadUser().subscribe();
    this.loadCalendars();
  }

  onLogout() {
    this.authState.logout();
  }

  loadCalendars() {
    this.loading.set(true);
    this.calendarService.index().subscribe({
      next: (calendars) => {
        this.calendars.set(calendars);
        this.loading.set(false);
      },
      error: (error) => {
        console.error('Error loading calendars:', error);
        this.loading.set(false);
        // Set empty array on error
        this.calendars.set([]);
      }
    });
  }

  createCalendar() {
    this.router.navigate(['/calendars/create']);
  }

  closeCreateModal() {
    this.showCreateCalendarModal.set(false);
  }

  onCreateCalendar() {
    const formData = this.calendarForm();
    
    if (!formData.name.trim()) {
      alert('Please enter a calendar name');
      return;
    }

    this.loading.set(true);

    this.calendarService.store(formData).subscribe({
      next: (newCalendar) => {
        this.calendars.update(calendars => [...calendars, newCalendar]);
        this.closeCreateModal();
        this.loading.set(false);
        console.log('Calendar created successfully:', newCalendar);
      },
      error: (error) => {
        console.error('Error creating calendar:', error);
        this.loading.set(false);
        alert('Failed to create calendar. Please try again.');
      }
    });
  }

  deleteCalendar(calendar: BookingCalendar) {
    if (!confirm(`Are you sure you want to delete "${calendar.name}"?`)) {
      return;
    }

    this.calendarService.delete(calendar.id).subscribe({
      next: () => {
        this.calendars.update(calendars => 
          calendars.filter(c => c.id !== calendar.id)
        );
        console.log('Calendar deleted successfully');
      },
      error: (error) => {
        console.error('Error deleting calendar:', error);
        alert('Failed to delete calendar. Please try again.');
      }
    });
  }

  copyCalendarLink(calendar: BookingCalendar) {
    // Get signed URL to copy
    this.calendarService.getSignedCalendarUrl(calendar.slug).subscribe({
      next: (response) => {
        navigator.clipboard.writeText(response.url).then(() => {
          alert('Calendar link copied to clipboard!');
        }).catch(err => {
          console.error('Failed to copy link:', err);
          alert('Failed to copy link to clipboard');
        });
      },
      error: (error) => {
        console.error('Failed to get signed calendar URL:', error);
        alert('Failed to get calendar link. Please try again.');
      }
    });
  }

  viewCalendar(calendar: BookingCalendar) {
    // Get signed URL and open in new tab
    this.calendarService.getSignedCalendarUrl(calendar.slug).subscribe({
      next: (response) => {
        window.open(response.url, '_blank');
      },
      error: (error) => {
        console.error('Failed to get signed calendar URL:', error);
        alert('Failed to open calendar. Please try again.');
      }
    });
  }

  manageCalendar(calendar: BookingCalendar) {
    // Navigate to calendar management page
    this.router.navigate(['/dashboard/calendars', calendar.id]);
  }

  navigateTo(route: string) {
    this.router.navigate([route]);
  }

  livePreview() {
    alert('Live preview feature coming soon!');
  }

  // Schedule management methods
  addScheduleForDay(dayOfWeek: string) {
    const currentForm = this.calendarForm();
    const existingSchedule = currentForm.schedules?.find(s => s.day_of_week === dayOfWeek);
    
    if (!existingSchedule) {
      this.calendarForm.update(form => ({
        ...form,
        schedules: [...(form.schedules || []), {
          day_of_week: dayOfWeek,
          time_slots: [{
            start_time: '09:00',
            end_time: '17:00'
          }]
        }]
      }));
    }
  }

  removeScheduleForDay(dayOfWeek: string) {
    this.calendarForm.update(form => ({
      ...form,
      schedules: form.schedules?.filter(s => s.day_of_week !== dayOfWeek) || []
    }));
  }

  addTimeSlot(dayOfWeek: string) {
    this.calendarForm.update(form => ({
      ...form,
      schedules: form.schedules?.map(schedule => 
        schedule.day_of_week === dayOfWeek 
          ? {
              ...schedule,
              time_slots: [...schedule.time_slots, {
                start_time: '09:00',
                end_time: '17:00'
              }]
            }
          : schedule
      ) || []
    }));
  }

  removeTimeSlot(dayOfWeek: string, slotIndex: number) {
    this.calendarForm.update(form => ({
      ...form,
      schedules: form.schedules?.map(schedule => 
        schedule.day_of_week === dayOfWeek 
          ? {
              ...schedule,
              time_slots: schedule.time_slots.filter((_, index) => index !== slotIndex)
            }
          : schedule
      ) || []
    }));
  }

  updateTimeSlot(dayOfWeek: string, slotIndex: number, field: 'start_time' | 'end_time', value: string) {
    this.calendarForm.update(form => ({
      ...form,
      schedules: form.schedules?.map(schedule => 
        schedule.day_of_week === dayOfWeek 
          ? {
              ...schedule,
              time_slots: schedule.time_slots.map((slot, index) => 
                index === slotIndex ? { ...slot, [field]: value } : slot
              )
            }
          : schedule
      ) || []
    }));
  }

  getScheduleForDay(dayOfWeek: string) {
    return this.calendarForm().schedules?.find(s => s.day_of_week === dayOfWeek);
  }

  isDayEnabled(dayOfWeek: string): boolean {
    return !!this.getScheduleForDay(dayOfWeek);
  }

  // Form helper methods
  updateCalendarForm(field: keyof CreateBookingCalendarRequest, value: any) {
    this.calendarForm.update(form => ({
      ...form,
      [field]: value
    }));
  }

  getColorName(colorValue: string): string {
    const color = this.availableColors.find(c => c.value === colorValue);
    return color ? color.name : 'Custom';
  }
}
