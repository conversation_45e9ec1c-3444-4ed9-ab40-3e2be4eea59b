<!-- Header -->
<header class="bg-white shadow-sm">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center h-16">
      <!-- Logo -->
      <div class="flex items-center space-x-2">
        <div class="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
          <span class="text-white font-bold text-sm">B</span>
        </div>
        <span class="text-xl font-semibold text-gray-900">BookEasy Pro</span>
      </div>
      
      <!-- Navigation -->
      <nav class="hidden md:flex space-x-8">
        <a href="#" class="text-gray-700 hover:text-orange-500 font-medium">Home</a>
        <a href="#" class="text-gray-700 hover:text-orange-500 font-medium">Features</a>
        <a href="#" class="text-gray-700 hover:text-orange-500 font-medium">Analytics</a>
        <a href="#" class="text-gray-700 hover:text-orange-500 font-medium">Themes</a>
      </nav>
      
      <!-- Actions -->
      <div class="flex items-center space-x-4">
        <button class="text-gray-700 hover:text-orange-500 font-medium">Sign In</button>
        <button class="bg-teal-500 text-white px-4 py-2 rounded-lg hover:bg-teal-600 font-medium">
          Start Tracking Free
        </button>
      </div>
    </div>
  </div>
</header>

<!-- Main Content -->
<main class="bg-gray-50 min-h-screen">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      
      <!-- Left Column - Hero Content -->
      <div class="space-y-8">
        <div class="space-y-4">
          <h1 class="text-5xl font-bold text-gray-900 leading-tight">
            The Scheduling
            <span class="text-orange-500">Platform</span> That Tracks
            Everything
          </h1>
          <p class="text-xl text-gray-600 leading-relaxed">
            Built for entrepreneurs & marketers who need to track every
            click, conversion, and dollar. Get <span class="font-semibold">deep analytics</span>, <span class="font-semibold">thank you page upsells</span>, and <span class="font-semibold">conversion tracking</span> that actually works 📊
          </p>
        </div>
        
        <button 
          class="bg-orange-500 text-white px-8 py-4 rounded-lg text-lg font-semibold hover:bg-orange-600 transition-colors"
          (click)="onStartTrackingClick()"
          [disabled]="loading()">
          {{ loading() ? 'Starting...' : 'Start Tracking • €10/year' }}
        </button>
        
        <!-- Features -->
        <div class="space-y-6">
          <!-- Calendly Migration -->
          <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <div class="flex items-start space-x-3">
              <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-1">
                <span class="text-white text-sm">✓</span>
              </div>
              <div>
                <h3 class="font-semibold text-green-900 flex items-center">
                  📅 <span class="ml-2">FREE Migration from Calendly</span>
                </h3>
                <p class="text-green-700 text-sm mt-1">
                  Already using Calendly? We'll import all your existing bookings, event types, and customer data completely free. <span class="font-semibold">No setup fees, no migration costs.</span>
                </p>
                <p class="text-green-600 text-sm mt-1">Switch in minutes, not hours.</p>
                <button 
                  class="text-green-700 underline text-sm mt-2 hover:text-green-800"
                  (click)="onFreeMigrationClick()">
                  Start Free Import →
                </button>
              </div>
            </div>
          </div>
          
          <!-- Features Grid -->
          <div class="grid grid-cols-2 gap-4">
            <div class="flex items-center space-x-2">
              <span class="text-green-500">✓</span>
              <span class="text-gray-700">UTM & Campaign Tracking</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-green-500">✓</span>
              <span class="text-gray-700">Thank You Page Upsells</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-green-500">✓</span>
              <span class="text-gray-700">Conversion Funnel Analytics</span>
            </div>
            <div class="flex items-center space-x-2">
              <span class="text-green-500">✓</span>
              <span class="text-gray-700">Custom High-Converting Themes</span>
            </div>
          </div>
          
          <!-- Pricing -->
          <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <div class="flex items-center space-x-2">
              <span class="text-2xl">💶</span>
              <div>
                <span class="font-semibold text-green-900">€10/year - Cheapest in the market</span>
                <p class="text-green-700 text-sm">Less than €1 per month for full tracking suite</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Right Column - Live Demo -->
      <div class="bg-white rounded-lg shadow-lg p-6">
        <div class="space-y-6">
          <!-- Demo Header -->
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            <h3 class="font-semibold text-gray-900">📊 Live Conversion Demo</h3>
          </div>
          <p class="text-gray-600 text-sm">
            See how our analytics track every interaction from this demo booking 👁️
          </p>
          
          <!-- Analytics Stats -->
          <div class="grid grid-cols-3 gap-4 text-center">
            <div>
              <div class="text-2xl font-bold text-orange-500">{{ analytics().pageViews }}</div>
              <div class="text-sm text-gray-600">Page Views</div>
            </div>
            <div>
              <div class="text-2xl font-bold text-green-500">{{ analytics().bookings }}</div>
              <div class="text-sm text-gray-600">Bookings</div>
            </div>
            <div>
              <div class="text-2xl font-bold text-blue-500">{{ analytics().conversionRate }}%</div>
              <div class="text-sm text-gray-600">Convert Rate</div>
            </div>
          </div>
          
          <!-- Mini Calendar -->
          <div class="bg-gray-50 rounded-lg p-4">
            <div class="text-center text-sm text-gray-600 mb-3 font-medium">August 2025</div>
            <div class="grid grid-cols-7 gap-1 text-center text-sm">
              <div class="text-gray-400 font-medium">S</div>
              <div class="text-gray-400 font-medium">M</div>
              <div class="text-gray-400 font-medium">T</div>
              <div class="text-gray-400 font-medium">W</div>
              <div class="text-gray-400 font-medium">T</div>
              <div class="text-gray-400 font-medium">F</div>
              <div class="text-gray-400 font-medium">S</div>
              
              <div class="p-2"></div>
              <div class="p-2"></div>
              <div class="p-2"></div>
              <div class="p-2"></div>
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">1</div>
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">2</div>
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">3</div>
              
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">4</div>
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">5</div>
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">6</div>
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">7</div>
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">8</div>
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">9</div>
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">10</div>
              
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">11</div>
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">12</div>
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">13</div>
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">14</div>
              <div class="p-2 bg-orange-500 text-white rounded font-medium shadow-sm">15</div>
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">16</div>
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">17</div>
              
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">18</div>
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">19</div>
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">20</div>
              <div class="p-2 bg-blue-500 text-white rounded font-medium shadow-sm">21</div>
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">22</div>
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">23</div>
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">24</div>
              
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">25</div>
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">26</div>
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">27</div>
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">28</div>
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">29</div>
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">30</div>
              <div class="p-2 hover:bg-gray-200 cursor-pointer rounded">31</div>
            </div>
          </div>
          
          <!-- CTA Button -->
          <button 
            class="w-full bg-teal-500 text-white py-3 rounded-lg font-semibold hover:bg-teal-600 transition-colors"
            (click)="onDemoBookingClick()">
            🔗 Track This Demo Booking
          </button>
          
          <p class="text-center text-sm text-gray-500">
            <span class="underline cursor-pointer hover:text-gray-700">See live analytics as you complete the booking</span>
          </p>
        </div>
      </div>
    </div>
    
    <!-- Bottom Testimonial -->
    <div class="mt-16 text-center">
      <blockquote class="text-lg italic text-gray-600 max-w-3xl mx-auto mb-6">
        "Finally, a scheduling tool that understands marketing. The analytics are 
        incredible and the upsell features have increased our revenue by 300%."
      </blockquote>
      
      <!-- Customer Avatar and Info -->
      <div class="flex items-center justify-center space-x-3">
        <div class="w-12 h-12 bg-orange-500 rounded-full flex items-center justify-center">
          <span class="text-white font-bold text-lg">M</span>
        </div>
        <div class="text-left">
          <div class="font-semibold text-gray-900">Michael Rodriguez</div>
          <div class="text-sm text-gray-600">Growth Marketing Lead at Techscale</div>
          <div class="text-sm text-gray-500">Generated $230k in tracked conversions</div>
        </div>
      </div>
    </div>
  </div>
</main>

<!-- Marketing Features (added) -->
<section class="bg-white py-16 border-t">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="text-center mb-10">
      <div class="inline-block px-3 py-1 rounded-full bg-orange-50 text-orange-600 font-medium text-sm mb-3">Marketing Features</div>
      <h2 class="text-3xl md:text-4xl font-bold text-gray-900">Built for entrepreneurs & marketers who <span class="text-orange-500">track everything</span></h2>
      <p class="mt-3 text-gray-600 max-w-2xl mx-auto">Every feature designed to maximize conversions, track performance, and optimize your booking funnel. Finally, a scheduling tool that understands business growth.</p>
    </div>

    <!-- Features grid -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <div class="p-5 bg-gray-50 rounded-lg feature-card">
        <div class="flex items-start space-x-3">
          <div class="w-10 h-10 bg-orange-100 text-orange-600 rounded-lg flex items-center justify-center font-semibold">📈</div>
          <div>
            <div class="text-sm text-gray-500 uppercase tracking-wide">Analytics</div>
            <div class="font-semibold text-gray-900">Deep Conversion Analytics</div>
            <p class="text-sm text-gray-600 mt-1">Track every step of your booking funnel and optimize for maximum conversions.</p>
          </div>
        </div>
      </div>

      <div class="p-5 bg-gray-50 rounded-lg feature-card">
        <div class="flex items-start space-x-3">
          <div class="w-10 h-10 bg-orange-100 text-orange-600 rounded-lg flex items-center justify-center font-semibold">💸</div>
          <div>
            <div class="text-sm text-gray-500 uppercase tracking-wide">Revenue</div>
            <div class="font-semibold text-gray-900">Thank You Page Upsells</div>
            <p class="text-sm text-gray-600 mt-1">Maximize revenue with custom thank you pages featuring product offers and strategic upsells.</p>
          </div>
        </div>
      </div>

      <div class="p-5 bg-gray-50 rounded-lg feature-card">
        <div class="flex items-start space-x-3">
          <div class="w-10 h-10 bg-orange-100 text-orange-600 rounded-lg flex items-center justify-center font-semibold">🎯</div>
          <div>
            <div class="text-sm text-gray-500 uppercase tracking-wide">Attribution</div>
            <div class="font-semibold text-gray-900">UTM & Campaign Tracking</div>
            <p class="text-sm text-gray-600 mt-1">Track the performance of every marketing campaign with detailed attribution and ROI metrics.</p>
          </div>
        </div>
      </div>

      <div class="p-5 bg-gray-50 rounded-lg feature-card">
        <div class="flex items-start space-x-3">
          <div class="w-10 h-10 bg-orange-100 text-orange-600 rounded-lg flex items-center justify-center font-semibold">🎨</div>
          <div>
            <div class="text-sm text-gray-500 uppercase tracking-wide">Design</div>
            <div class="font-semibold text-gray-900">High-Converting Themes</div>
            <p class="text-sm text-gray-600 mt-1">Choose from professionally designed themes optimized for conversions and brand consistency.</p>
          </div>
        </div>
      </div>

      <div class="p-5 bg-gray-50 rounded-lg feature-card">
        <div class="flex items-start space-x-3">
          <div class="w-10 h-10 bg-orange-100 text-orange-600 rounded-lg flex items-center justify-center font-semibold">⚙️</div>
          <div>
            <div class="text-sm text-gray-500 uppercase tracking-wide">Optimization</div>
            <div class="font-semibold text-gray-900">Funnel Optimization</div>
            <p class="text-sm text-gray-600 mt-1">Identify bottlenecks in your booking process and A/B test different flows for better results.</p>
          </div>
        </div>
      </div>

      <div class="p-5 bg-gray-50 rounded-lg feature-card">
        <div class="flex items-start space-x-3">
          <div class="w-10 h-10 bg-orange-100 text-orange-600 rounded-lg flex items-center justify-center font-semibold">🔌</div>
          <div>
            <div class="text-sm text-gray-500 uppercase tracking-wide">Integrations</div>
            <div class="font-semibold text-gray-900">Marketing Integrations</div>
            <p class="text-sm text-gray-600 mt-1">Connect with Google Analytics, Facebook Pixel, and your favorite marketing automation tools.</p>
          </div>
        </div>
      </div>

      <div class="p-5 bg-gray-50 rounded-lg feature-card">
        <div class="flex items-start space-x-3">
          <div class="w-10 h-10 bg-orange-100 text-orange-600 rounded-lg flex items-center justify-center font-semibold">🧾</div>
          <div>
            <div class="text-sm text-gray-500 uppercase tracking-wide">Qualifying</div>
            <div class="font-semibold text-gray-900">Lead Qualification</div>
            <p class="text-sm text-gray-600 mt-1">Custom intake forms and questionnaires to qualify leads before they book valuable time.</p>
          </div>
        </div>
      </div>

      <div class="p-5 bg-gray-50 rounded-lg feature-card">
        <div class="flex items-start space-x-3">
          <div class="w-10 h-10 bg-orange-100 text-orange-600 rounded-lg flex items-center justify-center font-semibold">🔒</div>
          <div>
            <div class="text-sm text-gray-500 uppercase tracking-wide">Compliant</div>
            <div class="font-semibold text-gray-900">GDPR & Privacy Ready</div>
            <p class="text-sm text-gray-600 mt-1">Built with privacy-first principles and full GDPR compliance for global marketing campaigns.</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Metrics row -->
    <div class="grid grid-cols-1 sm:grid-cols-4 gap-6 text-center mb-8">
      <div>
        <div class="text-2xl font-bold text-green-600">150%</div>
        <div class="text-sm text-gray-600">Average conversion increase</div>
      </div>
      <div>
        <div class="text-2xl font-bold text-gray-900">$2.4M+</div>
        <div class="text-sm text-gray-600">Revenue tracked and attributed</div>
      </div>
      <div>
        <div class="text-2xl font-bold text-green-600">85%</div>
        <div class="text-sm text-gray-600">Entrepreneurs & marketing teams love our analytics</div>
      </div>
      <div>
        <div class="text-2xl font-bold text-green-600">24/7</div>
        <div class="text-sm text-gray-600">Conversion tracking</div>
      </div>
    </div>

    <!-- CTA box -->
    <div class="rounded-lg p-6 md:p-8 bg-gradient-to-r from-orange-50 to-teal-50 border">
      <div class="md:flex md:items-center md:justify-between">
        <div class="mb-4 md:mb-0">
          <h3 class="text-xl font-semibold text-gray-900">Ready to track everything and optimize your booking funnel?</h3>
          <p class="text-sm text-gray-600 mt-1">Join 5,000+ entrepreneurs & marketers who use BookEasy Pro to maximize their conversion rates</p>
        </div>
        <div class="flex space-x-3">
          <button class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-md font-semibold">Start Free Trial</button>
          <button class="border border-orange-300 text-orange-600 px-4 py-2 rounded-md font-medium bg-white">Schedule Demo</button>
        </div>
      </div>
    </div>
  </div>
</section>
