import { Component, signal } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-index',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './index.component.html',
  styleUrl: './index.component.scss'
})
export class IndexComponent {
  // Signals for state management (following Angular constitution)
  loading = signal(false);
  demoCalendarId = signal('demo-cal-2025');
  analytics = signal({
    pageViews: 247,
    bookings: 31,
    conversionRate: 12.5
  });
  
  onStartTrackingClick() {
    console.log('Start tracking clicked!');
    // This will eventually navigate to registration/setup flow
    this.loading.set(true);
    
    // Simulate navigation delay
    setTimeout(() => {
      this.loading.set(false);
      console.log('Ready to start tracking setup...');
    }, 1000);
  }
  
  onDemoBookingClick() {
    console.log('Demo booking clicked!');
    // This will eventually open the booking modal or navigate to booking page
    
    // Simulate analytics increment
    const currentAnalytics = this.analytics();
    this.analytics.set({
      ...currentAnalytics,
      pageViews: currentAnalytics.pageViews + 1
    });
  }
  
  onFreeMigrationClick() {
    console.log('Free migration clicked!');
    // This will eventually navigate to Calendly import flow
  }
}
