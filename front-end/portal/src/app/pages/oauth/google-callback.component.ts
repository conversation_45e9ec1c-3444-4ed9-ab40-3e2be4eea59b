import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { GoogleIntegrationService } from '../../services/google-integration.service';

@Component({
  selector: 'app-google-callback',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="min-h-screen flex items-center justify-center bg-gray-100">
      <div class="bg-white p-8 rounded-lg shadow-md max-w-md w-full text-center">
        
        <!-- Loading State -->
        <div *ngIf="isProcessing" class="space-y-4">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto"></div>
          <h2 class="text-xl font-semibold text-gray-900">Connecting your Google account...</h2>
          <p class="text-gray-600">Please wait while we complete the setup.</p>
        </div>

        <!-- Success State -->
        <div *ngIf="!isProcessing && success" class="space-y-4">
          <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center mx-auto">
            <i class="fas fa-check text-white text-xl"></i>
          </div>
          <h2 class="text-xl font-semibold text-green-900">Successfully Connected!</h2>
          <p class="text-gray-600">Your Google account has been connected. You can now close this window.</p>
          <button 
            (click)="closeWindow()"
            class="px-6 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
          >
            Close Window
          </button>
        </div>

        <!-- Error State -->
        <div *ngIf="!isProcessing && !success && error" class="space-y-4">
          <div class="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center mx-auto">
            <i class="fas fa-times text-white text-xl"></i>
          </div>
          <h2 class="text-xl font-semibold text-red-900">Connection Failed</h2>
          <p class="text-gray-600">{{ error }}</p>
          <div class="space-x-4">
            <button 
              (click)="retry()"
              class="px-6 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors"
            >
              Try Again
            </button>
            <button 
              (click)="closeWindow()"
              class="px-6 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors"
            >
              Close
            </button>
          </div>
        </div>

      </div>
    </div>
  `,
  styles: [`
    .animate-spin {
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }
  `]
})
export class GoogleCallbackComponent implements OnInit {
  isProcessing = true;
  success = false;
  error: string | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private googleIntegrationService: GoogleIntegrationService
  ) {}

  ngOnInit(): void {
    this.route.queryParams.subscribe(params => {
      const code = params['code'];
      const error = params['error'];

      if (error) {
        this.handleError('Authorization was denied or cancelled');
        return;
      }

      if (code) {
        this.processAuthCode(code);
      } else {
        this.handleError('No authorization code received');
      }
    });
  }

  private processAuthCode(code: string): void {
    this.googleIntegrationService.handleCallback(code).subscribe({
      next: (response) => {
        this.isProcessing = false;
        if (response.success) {
          this.success = true;
          // Notify parent window if this is a popup
          if (window.opener) {
            window.opener.postMessage({ 
              type: 'GOOGLE_AUTH_SUCCESS', 
              email: response.google_email 
            }, '*');
          }
        } else {
          this.error = response.error || 'Unknown error occurred';
        }
      },
      error: (error) => {
        this.handleError('Failed to process authorization: ' + (error.error?.message || error.message));
      }
    });
  }

  private handleError(message: string): void {
    this.isProcessing = false;
    this.error = message;
    
    // Notify parent window if this is a popup
    if (window.opener) {
      window.opener.postMessage({ 
        type: 'GOOGLE_AUTH_ERROR', 
        error: message 
      }, '*');
    }
  }

  retry(): void {
    this.router.navigate(['/settings']);
    this.closeWindow();
  }

  closeWindow(): void {
    if (window.opener) {
      window.close();
    } else {
      this.router.navigate(['/settings']);
    }
  }
}