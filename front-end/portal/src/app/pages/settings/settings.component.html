<div class="p-8 max-w-4xl">
  <!-- <PERSON> Header -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900">Settings</h1>
    <p class="text-gray-600 mt-2">Manage your account settings and integrations</p>
  </div>

  <!-- Google Integration Section -->
  <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
    <!-- Section Header -->
    <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center space-x-3">
        <div class="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
          <i class="fab fa-google text-white text-sm"></i>
        </div>
        <div>
          <h2 class="text-lg font-semibold text-gray-900">Google Calendar Integration</h2>
          <p class="text-sm text-gray-600">Connect your Google Calendar to sync appointments and enable Google Meet</p>
        </div>
      </div>
    </div>

    <!-- Section Content -->
    <div class="p-6">
      <!-- Loading State -->
      <div *ngIf="isLoading" class="flex items-center justify-center py-8">
        <div class="flex items-center space-x-3">
          <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-orange-500"></div>
          <span class="text-gray-600">Loading Google connection status...</span>
        </div>
      </div>

      <!-- Content when loaded -->
      <div *ngIf="!isLoading" class="space-y-6">
        
        <!-- Connected State -->
        <div *ngIf="isGoogleConnected" class="space-y-4">
          <!-- Connection Status -->
          <div class="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg">
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                <i class="fas fa-check text-white"></i>
              </div>
              <div>
                <h3 class="font-medium text-green-900">Google Account Connected</h3>
                <p class="text-sm text-green-700">{{ googleEmail }}</p>
                <p *ngIf="isTokenExpired" class="text-sm text-amber-600 mt-1">
                  <i class="fas fa-exclamation-triangle mr-1"></i>
                  Token expired - please reconnect to continue syncing
                </p>
              </div>
            </div>
            <button
              (click)="disconnectGoogle()"
              [disabled]="isDisconnecting"
              class="px-4 py-2 text-sm font-medium text-red-700 bg-white border border-red-300 rounded-md hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <span *ngIf="!isDisconnecting">Disconnect</span>
              <span *ngIf="isDisconnecting" class="flex items-center space-x-2">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-red-700"></div>
                <span>Disconnecting...</span>
              </span>
            </button>
          </div>

          <!-- Features when connected -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div class="p-4 border border-gray-200 rounded-lg">
              <div class="flex items-center space-x-2 mb-2">
                <i class="fas fa-sync text-orange-500"></i>
                <h4 class="font-medium text-gray-900">Calendar Sync</h4>
              </div>
              <p class="text-sm text-gray-600">Your appointments are automatically synced to Google Calendar</p>
            </div>
            
            <div class="p-4 border border-gray-200 rounded-lg">
              <div class="flex items-center space-x-2 mb-2">
                <i class="fas fa-video text-blue-500"></i>
                <h4 class="font-medium text-gray-900">Google Meet</h4>
              </div>
              <p class="text-sm text-gray-600">Video meeting links are automatically generated for appointments</p>
            </div>
          </div>

          <!-- Reconnect button if token expired -->
          <div *ngIf="isTokenExpired" class="text-center">
            <button
              (click)="connectGoogle()"
              [disabled]="isConnecting"
              class="inline-flex items-center px-6 py-3 bg-orange-500 text-white font-medium rounded-lg hover:bg-orange-600 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <span *ngIf="!isConnecting">Reconnect Google Account</span>
              <span *ngIf="isConnecting" class="flex items-center space-x-2">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Connecting...</span>
              </span>
            </button>
          </div>
        </div>

        <!-- Disconnected State -->
        <div *ngIf="!isGoogleConnected" class="space-y-4">
          <!-- Not Connected Status -->
          <div class="flex items-center justify-between p-4 bg-gray-50 border border-gray-200 rounded-lg">
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-gray-400 rounded-full flex items-center justify-center">
                <i class="fas fa-times text-white"></i>
              </div>
              <div>
                <h3 class="font-medium text-gray-900">Google Account Not Connected</h3>
                <p class="text-sm text-gray-600">Connect your Google account to enable calendar sync and Google Meet integration</p>
              </div>
            </div>
          </div>

          <!-- Features available when connected -->
          <div class="space-y-3">
            <h4 class="font-medium text-gray-900">Benefits of connecting your Google account:</h4>
            <ul class="space-y-2">
              <li class="flex items-start space-x-3">
                <i class="fas fa-check text-green-500 mt-1"></i>
                <span class="text-sm text-gray-600">Automatic calendar synchronization</span>
              </li>
              <li class="flex items-start space-x-3">
                <i class="fas fa-check text-green-500 mt-1"></i>
                <span class="text-sm text-gray-600">Google Meet links for video appointments</span>
              </li>
              <li class="flex items-start space-x-3">
                <i class="fas fa-check text-green-500 mt-1"></i>
                <span class="text-sm text-gray-600">Two-way sync with your existing Google Calendar</span>
              </li>
              <li class="flex items-start space-x-3">
                <i class="fas fa-check text-green-500 mt-1"></i>
                <span class="text-sm text-gray-600">Automatic reminder notifications</span>
              </li>
            </ul>
          </div>

          <!-- Connect Button -->
          <div class="text-center pt-4">
            <button
              (click)="connectGoogle()"
              [disabled]="isConnecting"
              class="inline-flex items-center px-8 py-4 bg-red-500 text-white font-medium rounded-lg hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors shadow-lg"
            >
              <i class="fab fa-google mr-3"></i>
              <span *ngIf="!isConnecting">Connect Google Account</span>
              <span *ngIf="isConnecting" class="flex items-center space-x-2">
                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Connecting...</span>
              </span>
            </button>
          </div>
        </div>
      </div>

      <!-- Error State -->

    </div>
  </div>

  <!-- Additional Settings Sections -->
  <div class="mt-8 space-y-6">
    <!-- Account Settings Section -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
            <i class="fas fa-user text-white text-sm"></i>
          </div>
          <div>
            <h2 class="text-lg font-semibold text-gray-900">Account Settings</h2>
            <p class="text-sm text-gray-600">Manage your account preferences</p>
          </div>
        </div>
      </div>
      <div class="p-6">
        <p class="text-gray-600">Additional account settings will be added here in future updates.</p>
      </div>
    </div>

    <!-- Notification Settings Section -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200 bg-gray-50">
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
            <i class="fas fa-bell text-white text-sm"></i>
          </div>
          <div>
            <h2 class="text-lg font-semibold text-gray-900">Notification Settings</h2>
            <p class="text-sm text-gray-600">Configure how you receive notifications</p>
          </div>
        </div>
      </div>
      <div class="p-6">
        <p class="text-gray-600">Notification preferences will be available in future updates.</p>
      </div>
    </div>
  </div>
</div>