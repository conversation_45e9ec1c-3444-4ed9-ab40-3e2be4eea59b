import { Component, OnInit, On<PERSON><PERSON>roy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute } from '@angular/router';
import { GoogleIntegrationService, GoogleStatus } from '../../services/google-integration.service';
import {Subject, tap} from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import {GoogleAuthenticationService} from "@api/google/services/google-authentication.service";

@Component({
  selector: 'app-settings',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './settings.component.html',
  styleUrls: ['./settings.component.scss']
})
export class SettingsComponent implements OnInit, OnDestroy {
  private destroy$ = new Subject<void>();
  private googleIntegrationService = inject(GoogleIntegrationService);
  private route = inject(ActivatedRoute);
  private googleAuthenticationService = inject(GoogleAuthenticationService)
  
  googleStatus: GoogleStatus | null = null;
  isLoading = false;
  isConnecting = false;
  isDisconnecting = false;

  ngOnInit(): void {
    // Check for Google OAuth callback parameters
    this.route.queryParams.subscribe(params => {
      const googleCode = params['google_code'];
      const googleError = params['google_error'];
      
      if (googleCode) {
        this.handleGoogleCallback(googleCode);
      } else if (googleError) {
        console.error('Google OAuth error:', googleError);
        this.isConnecting = false;
      }
    });

    // Subscribe to Google status changes
    this.googleIntegrationService.status$
      .pipe(takeUntil(this.destroy$))
      .subscribe(status => {
        // this.googleStatus = GoogleStat;
        this.isLoading = false;
        this.isConnecting = false;
        this.isDisconnecting = false;
      });

    this.loadGoogleStatus();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  loadGoogleStatus(): void {
    // this.isLoading = true;
    this.googleIntegrationService.loadStatus();
  }

  handleGoogleCallback(code: string): void {
    this.isConnecting = true;
    this.googleIntegrationService.handleCallback(code).subscribe({
      next: (response) => {
        if (response.success) {
          console.log('Google account connected successfully');
          // Clear URL parameters
          window.history.replaceState({}, document.title, window.location.pathname);
        } else {
          console.error('Failed to connect Google account:', response.error);
        }
        this.isConnecting = false;
      },
      error: (error) => {
        console.error('Error connecting Google account:', error);
        this.isConnecting = false;
      }
    });
  }

  async connectGoogle(): Promise<void> {
    this.isConnecting = true;

    this.googleAuthenticationService.oauthRedirectLink().pipe(
        tap((url) => {
            window.open(url.data.url);
        })
    ).subscribe();
  }

  disconnectGoogle(): void {
    if (!confirm('Are you sure you want to disconnect your Google account? This will stop syncing your calendars and appointments.')) {
      return;
    }

    this.isDisconnecting = true;
    this.googleIntegrationService.disconnect().subscribe({
      next: (response) => {
        if (response.success) {
          console.log('Google account disconnected successfully');
        } else {
          console.error('Failed to disconnect Google account:', response.error);
        }
        this.isDisconnecting = false;
      },
      error: (error) => {
        console.error('Error disconnecting Google account:', error);
        this.isDisconnecting = false;
      }
    });
  }

  get isGoogleConnected(): boolean {
    return this.googleStatus?.is_connected ?? false;
  }

  get googleEmail(): string | null {
    return this.googleStatus?.google_email ?? null;
  }

  get isTokenExpired(): boolean {
    return this.googleStatus?.token_expired ?? false;
  }
}