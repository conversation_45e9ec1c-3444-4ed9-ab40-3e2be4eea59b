import { Injectable } from '@angular/core';
import { Config } from '../interfaces/config.interface';

@Injectable({
  providedIn: 'root',
})
export class ConfigService {
  // Constants
  private readonly LOCATION = './config.json';

  // State
  private loadedConfig: Config | undefined;

  public initialize(): Promise<Config> {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      xhr.open('GET', this.LOCATION);

      xhr.addEventListener('readystatechange', () => {
        if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
          const config = JSON.parse(xhr.responseText);
          this.loadedConfig = config;
          resolve(config);

          return;
        }

        if (xhr.readyState === XMLHttpRequest.DONE) {
          reject();
        }
      });

      xhr.send(null);
    });
  }

  public get config(): Config | undefined {
    return this.loadedConfig;
  }
}
