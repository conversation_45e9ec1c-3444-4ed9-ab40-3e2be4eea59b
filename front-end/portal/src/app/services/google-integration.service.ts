import { Injectable, inject } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject } from 'rxjs';
import { tap } from 'rxjs/operators';
import { ConfigService } from './config.service';

export interface GoogleStatus {
  is_connected: boolean;
  google_email?: string;
  token_expired?: boolean;
  success: boolean;
}

export interface GoogleAuthResponse {
  success: boolean;
  auth_url?: string;
  message?: string;
  google_email?: string;
  error?: string;
}

@Injectable({
  providedIn: 'root'
})
export class GoogleIntegrationService {
  private http = inject(HttpClient);
  private configService = inject(ConfigService);
  private endpoint = this.configService.config?.environment.endpoint;
  private baseUrl = `${this.endpoint}/api/v1/google`;
  
  private statusSubject = new BehaviorSubject<GoogleStatus | null>(null);
  
  public status$ = this.statusSubject.asObservable();

  constructor() {
    this.loadStatus();
  }

  /**
   * Get current Google connection status
   */
  getStatus(): Observable<GoogleStatus> {
    return this.http.get<GoogleStatus>(`${this.baseUrl}/status`).pipe(
      tap(status => this.statusSubject.next(status))
    );
  }

  /**
   * Load status and update internal state
   */
  loadStatus(): void {
    this.getStatus().subscribe({
      next: (status) => {
        console.log('Google status loaded:', status);
      },
      error: (error) => {
        console.error('Failed to load Google status:', error);
      }
    });
  }

  /**
   * Get Google OAuth authorization URL
   */
  getAuthUrl(): Observable<GoogleAuthResponse> {
    return this.http.get<GoogleAuthResponse>(`${this.baseUrl}/auth-url`);
  }

  /**
   * Handle OAuth callback (called from frontend after user returns from Google)
   */
  handleCallback(authorizationCode: string): Observable<GoogleAuthResponse> {
    return this.http.post<GoogleAuthResponse>(`${this.baseUrl}/callback`, {
      code: authorizationCode
    }).pipe(
      tap(response => {
        if (response.success) {
          this.loadStatus(); // Refresh status after successful connection
        }
      })
    );
  }

  /**
   * Disconnect Google account
   */
  disconnect(): Observable<GoogleAuthResponse> {
    return this.http.delete<GoogleAuthResponse>(`${this.baseUrl}/disconnect`).pipe(
      tap(response => {
        if (response.success) {
          this.loadStatus(); // Refresh status after disconnection
        }
      })
    );
  }

  /**
   * Open Google OAuth authorization in new window
   */
  async startOAuthFlow(): Promise<void> {
    try {
      const response = await this.getAuthUrl().toPromise();
      if (response?.success && response.auth_url) {
        // Instead of opening a popup, redirect the current window
        window.location.href = response.auth_url;
      }
    } catch (error) {
      console.error('Failed to start OAuth flow:', error);
      throw error;
    }
  }
}