import { Injectable, signal } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, Subject, tap, finalize } from 'rxjs';
import { DataResponse } from '@api/support/responses/data.response';
import { AuthenticationService } from '@api/auth/services/authentication.service';
import { Me } from '@api/auth/models/me.model';
import { LoginRequest } from '@api/auth/requests/login.request';

export const LOGGED_IN_LOCAL_STORAGE_KEY = 'logged_in';

// Export LoginCredentials interface for login component
export interface LoginCredentials {
  email: string;
  password: string;
  remember?: boolean;
}


@Injectable({
  providedIn: 'root'
})
export class AuthState {
    public user = signal<Me | null>(null);
    public loading = signal<boolean>(false);

  private impersonating = signal<boolean>(false);

  constructor(
    private router: Router,
    private authenticationService: AuthenticationService,
  ) {}

  public isLoggedIn(): boolean {
    return !!localStorage.getItem(LOGGED_IN_LOCAL_STORAGE_KEY);
  }

  public logout(): void {
    this.user.set(null);

    this.authenticationService.logout().subscribe();
    localStorage.removeItem(LOGGED_IN_LOCAL_STORAGE_KEY);

    this.router.navigate(['/auth']);
  }

  public login(credentials: LoginCredentials): Observable<void> {
    this.loading.set(true);
    
    const loginRequest: LoginRequest = {
      email: credentials.email,
      password: credentials.password
    };

    return this.authenticationService.login(loginRequest).pipe(
      tap(() => {
        localStorage.setItem(LOGGED_IN_LOCAL_STORAGE_KEY, 'true');
      }),
      finalize(() => {
        this.loading.set(false);
      })
    );
  }

  public loadUser(): Observable<DataResponse<Me>> {
    return this.authenticationService.me().pipe(
      tap((response: DataResponse<Me>) => {
        this.user.set(response.data);
      }),
    );
  }

  public isImpersonating(): boolean {
    return this.impersonating();
  }

  public setImpersonating(value: boolean): void {
    this.impersonating.set(value);
  }

}
