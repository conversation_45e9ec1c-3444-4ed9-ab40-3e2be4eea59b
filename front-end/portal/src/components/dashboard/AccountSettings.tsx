import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Check, User, Palette, Eye, ImageIcon, Video } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { useNavigate } from 'react-router-dom';

interface Theme {
  id: string;
  name: string;
  description: string;
  colors: any;
  fonts: any;
  layout_settings: any;
}

interface UserSettings {
  id?: string;
  user_id: string;
  theme_id?: string;
  show_header: boolean;
  google_meet_enabled: boolean;
}

export const AccountSettings = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [themes, setThemes] = useState<Theme[]>([]);
  const [userSettings, setUserSettings] = useState<UserSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadData();
    }
  }, [user]);

  const loadData = async () => {
    try {
      // Load themes (including custom ones)
      const { data: themesData, error: themesError } = await supabase
        .from('themes')
        .select('*')
        .order('name');

      if (themesError) {
        console.error('Error loading themes:', themesError);
      } else {
        setThemes(themesData || []);
      }

      // Load user settings
      const { data: settingsData, error: settingsError } = await supabase
        .from('user_settings')
        .select('*')
        .eq('user_id', user?.id)
        .single();

      if (settingsError && settingsError.code !== 'PGRST116') {
        console.error('Error loading user settings:', settingsError);
      } else if (settingsData) {
        setUserSettings(settingsData);
      } else {
        // Create default settings if none exist
        const { data: newSettings, error: createError } = await supabase
          .from('user_settings')
          .insert([{
            user_id: user?.id,
            show_header: true,
            google_meet_enabled: false
          }])
          .select()
          .single();

        if (createError) {
          console.error('Error creating user settings:', createError);
        } else {
          setUserSettings(newSettings);
        }
      }
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateTheme = async (themeId: string) => {
    if (!userSettings) return;

    try {
      const { error } = await supabase
        .from('user_settings')
        .update({ theme_id: themeId })
        .eq('user_id', user?.id);

      if (error) {
        toast({
          title: 'Error',
          description: error.message,
          variant: 'destructive',
        });
      } else {
        setUserSettings({ ...userSettings, theme_id: themeId });
        toast({
          title: 'Theme Updated!',
          description: 'Your account theme has been updated for all calendars.',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      });
    }
  };


  const updateGoogleMeetSetting = async (enabled: boolean) => {
    if (!userSettings) return;

    try {
      const { error } = await supabase
        .from('user_settings')
        .update({ google_meet_enabled: enabled })
        .eq('user_id', user?.id);

      if (error) {
        toast({
          title: 'Error',
          description: error.message,
          variant: 'destructive',
        });
      } else {
        setUserSettings({ ...userSettings, google_meet_enabled: enabled });
        toast({
          title: 'Google Meet Updated!',
          description: `Google Meet auto-generation ${enabled ? 'enabled' : 'disabled'}.`,
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      });
    }
  };


  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2 flex items-center">
          <User className="w-5 h-5 mr-2" />
          Account Settings
        </h3>
        <p className="text-muted-foreground">
          Manage global settings that apply to all your calendars
        </p>
      </div>

      {/* Global Theme Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-base">
            <Palette className="w-4 h-4 mr-2" />
            Global Theme
          </CardTitle>
          <CardDescription>
            Choose a theme that will be applied to all your calendars
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            {themes.map((theme) => (
              <Card
                key={theme.id}
                className={`cursor-pointer transition-all hover:shadow-lg ${
                  userSettings?.theme_id === theme.id ? 'ring-2 ring-primary' : ''
                }`}
              >
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center text-sm">
                      {theme.name}
                      {userSettings?.theme_id === theme.id && (
                        <Check className="w-4 h-4 ml-2 text-green-500" />
                      )}
                    </CardTitle>
                  </div>
                  <CardDescription className="text-xs">
                    {theme.description}
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  {/* Color Preview */}
                  <div className="flex space-x-1">
                    <div
                      className="w-6 h-6 rounded-full border-2 border-white shadow-sm"
                      style={{ backgroundColor: theme.colors?.primary || '#6366f1' }}
                      title="Primary Color"
                    />
                    <div
                      className="w-6 h-6 rounded-full border-2 border-white shadow-sm"
                      style={{ backgroundColor: theme.colors?.secondary || '#f8fafc' }}
                      title="Secondary Color"
                    />
                    <div
                      className="w-6 h-6 rounded-full border-2 border-white shadow-sm"
                      style={{ backgroundColor: theme.colors?.accent || '#0ea5e9' }}
                      title="Accent Color"
                    />
                  </div>

                  {/* Font & Layout Info */}
                  <div className="text-xs text-muted-foreground space-y-1">
                    <div>Font: {theme.fonts?.heading || 'Default'}</div>
                    <div>Layout: {theme.layout_settings?.layout || 'Default'}</div>
                  </div>

                  <Button
                    variant={userSettings?.theme_id === theme.id ? 'default' : 'outline'}
                    size="sm"
                    className="w-full"
                    onClick={() => updateTheme(theme.id)}
                    disabled={userSettings?.theme_id === theme.id}
                  >
                    {userSettings?.theme_id === theme.id ? 'Current Theme' : 'Select Theme'}
                  </Button>
                </CardContent>
              </Card>
            ))}
            
            {/* Create Custom Theme Card */}
            <Card className="cursor-pointer transition-all hover:shadow-lg border-dashed border-2 border-primary/30 hover:border-primary/50">
              <CardContent className="flex flex-col items-center justify-center p-8 space-y-4">
                <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                  <Palette className="w-6 h-6 text-primary" />
                </div>
                <div className="text-center space-y-2">
                  <CardTitle className="text-sm">Create Custom Theme</CardTitle>
                  <CardDescription className="text-xs">
                    Design your own theme with custom colors
                  </CardDescription>
                </div>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="w-full"
                  onClick={() => navigate('/create-theme')}
                >
                  Create Theme
                </Button>
              </CardContent>
            </Card>
          </div>
        </CardContent>
      </Card>

      {/* Google Meet Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-base">
            <Video className="w-4 h-4 mr-2" />
            Google Meet Integration
          </CardTitle>
          <CardDescription>
            Google Meet integration is now configured per calendar. You can enable it in each calendar's Visibility settings.
          </CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center text-muted-foreground">
            <Video className="w-8 h-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">
              Google Meet settings have been moved to individual calendar settings for better control.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};