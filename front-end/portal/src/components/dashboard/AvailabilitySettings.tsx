import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { toast } from '@/hooks/use-toast';
import { Clock, Plus, X } from 'lucide-react';

interface AvailabilitySettingsProps {
  calendarId: string;
}

interface TimeSlot {
  start_time: string;
  end_time: string;
}

interface DayAvailability {
  enabled: boolean;
  slots: TimeSlot[];
}

export const AvailabilitySettings = ({ calendarId }: AvailabilitySettingsProps) => {
  const [availability, setAvailability] = useState<Record<number, DayAvailability>>({
    1: { enabled: true, slots: [{ start_time: '09:00', end_time: '17:00' }] },
    2: { enabled: true, slots: [{ start_time: '09:00', end_time: '17:00' }] },
    3: { enabled: true, slots: [{ start_time: '09:00', end_time: '17:00' }] },
    4: { enabled: true, slots: [{ start_time: '09:00', end_time: '17:00' }] },
    5: { enabled: true, slots: [{ start_time: '09:00', end_time: '17:00' }] },
    6: { enabled: false, slots: [{ start_time: '10:00', end_time: '14:00' }] },
    0: { enabled: false, slots: [{ start_time: '10:00', end_time: '14:00' }] },
  });
  const [isLoading, setIsLoading] = useState(false);

  const days = [
    { key: 0, name: 'Sunday' },
    { key: 1, name: 'Monday' },
    { key: 2, name: 'Tuesday' },
    { key: 3, name: 'Wednesday' },
    { key: 4, name: 'Thursday' },
    { key: 5, name: 'Friday' },
    { key: 6, name: 'Saturday' },
  ];

  useEffect(() => {
    loadAvailability();
  }, [calendarId]);

  const loadAvailability = async () => {
    try {
      const { data, error } = await supabase
        .from('availability')
        .select('*')
        .eq('calendar_id', calendarId);

      if (error) {
        console.error('Error loading availability:', error);
        return;
      }

      if (data && data.length > 0) {
        const loadedAvailability: Record<number, DayAvailability> = {};
        
        // Initialize all days as disabled
        days.forEach(day => {
          loadedAvailability[day.key] = {
            enabled: false,
            slots: [{ start_time: '09:00', end_time: '17:00' }]
          };
        });

        // Set enabled days from database
        data.forEach(slot => {
          if (!loadedAvailability[slot.day_of_week]) {
            loadedAvailability[slot.day_of_week] = { enabled: true, slots: [] };
          }
          loadedAvailability[slot.day_of_week].enabled = true;
          loadedAvailability[slot.day_of_week].slots.push({
            start_time: slot.start_time,
            end_time: slot.end_time,
          });
        });

        setAvailability(loadedAvailability);
      }
    } catch (error) {
      console.error('Error loading availability:', error);
    }
  };

  const saveAvailability = async () => {
    setIsLoading(true);
    try {
      // First, delete existing availability for this calendar
      await supabase
        .from('availability')
        .delete()
        .eq('calendar_id', calendarId);

      // Then insert new availability
      const availabilityData = [];
      for (const [dayKey, dayAvailability] of Object.entries(availability)) {
        if (dayAvailability.enabled) {
          for (const slot of dayAvailability.slots) {
            availabilityData.push({
              calendar_id: calendarId,
              day_of_week: parseInt(dayKey),
              start_time: slot.start_time,
              end_time: slot.end_time,
            });
          }
        }
      }

      if (availabilityData.length > 0) {
        const { error } = await supabase
          .from('availability')
          .insert(availabilityData);

        if (error) {
          toast({
            title: 'Error',
            description: error.message,
            variant: 'destructive',
          });
        } else {
          toast({
            title: 'Success',
            description: 'Availability updated successfully!',
          });
        }
      } else {
        toast({
          title: 'Success',
          description: 'Availability cleared successfully!',
        });
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const toggleDay = (dayKey: number) => {
    setAvailability(prev => ({
      ...prev,
      [dayKey]: {
        ...prev[dayKey],
        enabled: !prev[dayKey].enabled,
      },
    }));
  };

  const updateTimeSlot = (dayKey: number, slotIndex: number, field: 'start_time' | 'end_time', value: string) => {
    setAvailability(prev => ({
      ...prev,
      [dayKey]: {
        ...prev[dayKey],
        slots: prev[dayKey].slots.map((slot, index) =>
          index === slotIndex ? { ...slot, [field]: value } : slot
        ),
      },
    }));
  };

  const addTimeSlot = (dayKey: number) => {
    setAvailability(prev => ({
      ...prev,
      [dayKey]: {
        ...prev[dayKey],
        slots: [...prev[dayKey].slots, { start_time: '09:00', end_time: '17:00' }],
      },
    }));
  };

  const removeTimeSlot = (dayKey: number, slotIndex: number) => {
    setAvailability(prev => ({
      ...prev,
      [dayKey]: {
        ...prev[dayKey],
        slots: prev[dayKey].slots.filter((_, index) => index !== slotIndex),
      },
    }));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Clock className="w-5 h-5 mr-2" />
          Availability Settings
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {days.map((day) => (
          <div key={day.key} className="space-y-3">
            <div className="flex items-center justify-between">
              <Label className="text-base font-medium">{day.name}</Label>
              <Switch
                checked={availability[day.key]?.enabled || false}
                onCheckedChange={() => toggleDay(day.key)}
              />
            </div>
            {availability[day.key]?.enabled && (
              <div className="pl-4 space-y-3">
                <div className="text-sm text-muted-foreground mb-2">
                  Time Slot{availability[day.key]?.slots.length > 1 ? 's' : ''} 
                  {availability[day.key]?.slots.length > 1 && (
                    <span className="ml-1">({availability[day.key]?.slots.length} slots)</span>
                  )}
                </div>
                {availability[day.key]?.slots.map((slot, slotIndex) => (
                  <div key={slotIndex} className="p-3 border rounded-lg bg-muted/20">
                    <div className="flex items-center gap-4">
                      <div className="grid grid-cols-2 gap-4 flex-1">
                        <div>
                          <Label htmlFor={`start-${day.key}-${slotIndex}`} className="text-xs">
                            Start Time {slotIndex > 0 && `#${slotIndex + 1}`}
                          </Label>
                          <Input
                            id={`start-${day.key}-${slotIndex}`}
                            type="time"
                            value={slot.start_time}
                            onChange={(e) => updateTimeSlot(day.key, slotIndex, 'start_time', e.target.value)}
                          />
                        </div>
                        <div>
                          <Label htmlFor={`end-${day.key}-${slotIndex}`} className="text-xs">
                            End Time {slotIndex > 0 && `#${slotIndex + 1}`}
                          </Label>
                          <Input
                            id={`end-${day.key}-${slotIndex}`}
                            type="time"
                            value={slot.end_time}
                            onChange={(e) => updateTimeSlot(day.key, slotIndex, 'end_time', e.target.value)}
                          />
                        </div>
                      </div>
                      {availability[day.key]?.slots.length > 1 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeTimeSlot(day.key, slotIndex)}
                          className="mt-4 text-destructive hover:text-destructive hover:bg-destructive/10"
                          title="Remove this time slot"
                        >
                          <X className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => addTimeSlot(day.key)}
                  className="mt-3 w-full border-dashed hover:border-solid"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add Another Time Slot
                </Button>
              </div>
            )}
          </div>
        ))}
        <Button onClick={saveAvailability} disabled={isLoading} className="w-full">
          {isLoading ? 'Saving...' : 'Save Availability'}
        </Button>
      </CardContent>
    </Card>
  );
};