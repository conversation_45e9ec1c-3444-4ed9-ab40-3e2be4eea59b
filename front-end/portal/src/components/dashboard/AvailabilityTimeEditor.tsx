import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { toast } from 'sonner';
import { Save, X, Plus, Trash2, Loader2 } from 'lucide-react';

interface AvailabilityRule {
  id?: string;
  day_of_week: number;
  start_time: string;
  end_time: string;
}

interface AvailabilityTimeEditorProps {
  scheduleId: string;
  scheduleName: string;
  onClose: () => void;
  onSaved: () => void;
}

const DAYS = [
  { value: 1, label: 'Monday', short: 'Mon' },
  { value: 2, label: 'Tuesday', short: 'Tue' },
  { value: 3, label: 'Wednesday', short: 'Wed' },
  { value: 4, label: 'Thursday', short: 'Thu' },
  { value: 5, label: 'Friday', short: 'Fri' },
  { value: 6, label: 'Saturday', short: 'Sat' },
  { value: 0, label: 'Sunday', short: 'Sun' },
];

export const AvailabilityTimeEditor = ({ scheduleId, scheduleName, onClose, onSaved }: AvailabilityTimeEditorProps) => {
  const [rules, setRules] = useState<AvailabilityRule[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadRules();
  }, [scheduleId]);

  const loadRules = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('availability_schedule_rules')
        .select('*')
        .eq('schedule_id', scheduleId)
        .order('day_of_week');

      if (error) throw error;

      setRules(data || []);
    } catch (error) {
      console.error('Error loading rules:', error);
      toast.error('Failed to load availability rules');
    } finally {
      setLoading(false);
    }
  };

  const addTimeSlot = (dayOfWeek: number) => {
    const newRule: AvailabilityRule = {
      day_of_week: dayOfWeek,
      start_time: '09:00',
      end_time: '17:00'
    };
    setRules([...rules, newRule]);
  };

  const removeTimeSlot = (index: number) => {
    setRules(rules.filter((_, i) => i !== index));
  };

  const updateTimeSlot = (index: number, field: 'start_time' | 'end_time', value: string) => {
    const updatedRules = [...rules];
    updatedRules[index] = { ...updatedRules[index], [field]: value };
    setRules(updatedRules);
  };

  const saveRules = async () => {
    try {
      setSaving(true);

      // Delete existing rules
      const { error: deleteError } = await supabase
        .from('availability_schedule_rules')
        .delete()
        .eq('schedule_id', scheduleId);

      if (deleteError) throw deleteError;

      // Insert new rules
      if (rules.length > 0) {
        const rulesToInsert = rules.map(rule => ({
          schedule_id: scheduleId,
          day_of_week: rule.day_of_week,
          start_time: rule.start_time,
          end_time: rule.end_time
        }));

        const { error: insertError } = await supabase
          .from('availability_schedule_rules')
          .insert(rulesToInsert);

        if (insertError) throw insertError;
      }

      toast.success('Availability schedule updated successfully');
      onSaved();
      onClose();
    } catch (error) {
      console.error('Error saving rules:', error);
      toast.error('Failed to save availability schedule');
    } finally {
      setSaving(false);
    }
  };

  const getRulesForDay = (dayOfWeek: number) => {
    return rules
      .map((rule, index) => ({ ...rule, index }))
      .filter(rule => rule.day_of_week === dayOfWeek);
  };

  const isDayEnabled = (dayOfWeek: number) => {
    return getRulesForDay(dayOfWeek).length > 0;
  };

  const toggleDay = (dayOfWeek: number) => {
    if (isDayEnabled(dayOfWeek)) {
      // Remove all rules for this day
      setRules(rules.filter(rule => rule.day_of_week !== dayOfWeek));
    } else {
      // Add default rule for this day
      addTimeSlot(dayOfWeek);
    }
  };

  if (loading) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle>Edit {scheduleName}</CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <div className="flex items-center justify-center py-8">
            <Loader2 className="w-6 h-6 animate-spin" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Edit {scheduleName}</CardTitle>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {DAYS.map((day) => {
          const dayRules = getRulesForDay(day.value);
          const isEnabled = isDayEnabled(day.value);

          return (
            <div key={day.value} className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Switch
                    checked={isEnabled}
                    onCheckedChange={() => toggleDay(day.value)}
                  />
                  <Label className="text-base font-medium">{day.label}</Label>
                </div>
                {isEnabled && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => addTimeSlot(day.value)}
                    className="flex items-center gap-1"
                  >
                    <Plus className="w-3 h-3" />
                    Add Time
                  </Button>
                )}
              </div>

              {isEnabled && (
                <div className="ml-8 space-y-2">
                  {dayRules.map((rule) => (
                    <div key={rule.index} className="flex items-center gap-3">
                      <div className="flex items-center gap-2">
                        <Input
                          type="time"
                          value={rule.start_time}
                          onChange={(e) => updateTimeSlot(rule.index!, 'start_time', e.target.value)}
                          className="w-32"
                        />
                        <span className="text-muted-foreground">to</span>
                        <Input
                          type="time"
                          value={rule.end_time}
                          onChange={(e) => updateTimeSlot(rule.index!, 'end_time', e.target.value)}
                          className="w-32"
                        />
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeTimeSlot(rule.index!)}
                        className="text-destructive hover:text-destructive"
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  ))}
                  {dayRules.length === 0 && (
                    <p className="text-sm text-muted-foreground ml-2">
                      No time slots set for this day
                    </p>
                  )}
                </div>
              )}
            </div>
          );
        })}

        <div className="flex justify-end gap-2 pt-4">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={saveRules} disabled={saving}>
            {saving ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                Save Schedule
              </>
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};