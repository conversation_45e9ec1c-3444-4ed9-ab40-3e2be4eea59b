import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { toast } from '@/hooks/use-toast';
import { Switch } from '@/components/ui/switch';
import { Plus, Video } from 'lucide-react';

const calendarSchema = z.object({
  name: z.string().min(1, 'Calendar name is required'),
  description: z.string().optional(),
  duration: z.number().min(15).max(480),
  color: z.string(),
  conversion_tracking: z.boolean().default(true),
  google_meet_enabled: z.boolean().default(false),
  google_meet_auto_generate: z.boolean().default(false),
  whats_next_enabled: z.boolean().default(false),
  whats_next_title: z.string().optional(),
  whats_next_description: z.string().optional(),
});

type CalendarFormData = z.infer<typeof calendarSchema>;

interface CalendarFormProps {
  onCalendarCreated: () => void;
}

export const CalendarForm = ({ onCalendarCreated }: CalendarFormProps) => {
  const [open, setOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<CalendarFormData>({
    resolver: zodResolver(calendarSchema),
    defaultValues: {
      name: '',
      description: '',
      duration: 30,
      color: '#6366f1',
      conversion_tracking: true,
      google_meet_enabled: false,
      google_meet_auto_generate: false,
      whats_next_enabled: false,
      whats_next_title: "What happens next?",
      whats_next_description: "After you book this session, you'll receive a confirmation email with all the details and next steps.",
    },
  });

  const onSubmit = async (data: CalendarFormData) => {
    setIsLoading(true);
    try {
      const { error } = await supabase
        .from('calendars')
        .insert([{
          name: data.name,
          description: data.description,
          duration: data.duration,
          color: data.color,
          conversion_tracking: data.conversion_tracking,
          google_meet_enabled: data.google_meet_enabled,
          google_meet_auto_generate: data.google_meet_auto_generate,
          whats_next_enabled: data.whats_next_enabled,
          whats_next_title: data.whats_next_title,
          whats_next_description: data.whats_next_description,
          user_id: (await supabase.auth.getUser()).data.user?.id,
        }]);

      if (error) {
        toast({
          title: 'Error',
          description: error.message,
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'Success',
          description: 'Calendar created successfully!',
        });
        form.reset();
        setOpen(false);
        onCalendarCreated();
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  const colors = [
    { value: '#6366f1', label: 'Indigo' },
    { value: '#ef4444', label: 'Red' },
    { value: '#10b981', label: 'Green' },
    { value: '#f59e0b', label: 'Orange' },
    { value: '#8b5cf6', label: 'Purple' },
    { value: '#06b6d4', label: 'Cyan' },
  ];

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="flex items-center">
          <Plus className="w-4 h-4 mr-2" />
          Create Calendar
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create New Calendar</DialogTitle>
          <DialogDescription>
            Set up a conversion-optimized booking calendar that tracks everything.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Calendar Name</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g., Strategy Session - 30min" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="What will visitors get from this booking? (helps with conversion)"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="duration"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Duration (minutes)</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      min="15"
                      max="480"
                      {...field}
                      onChange={(e) => field.onChange(parseInt(e.target.value))}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="color"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Color</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a color">
                          <div className="flex items-center">
                            <div
                              className="w-4 h-4 rounded-full mr-2"
                              style={{ backgroundColor: field.value }}
                            />
                            {colors.find(c => c.value === field.value)?.label}
                          </div>
                        </SelectValue>
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {colors.map((color) => (
                        <SelectItem key={color.value} value={color.value}>
                          <div className="flex items-center">
                            <div
                              className="w-4 h-4 rounded-full mr-2"
                              style={{ backgroundColor: color.value }}
                            />
                            {color.label}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="conversion_tracking"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                  <div className="space-y-0.5">
                    <FormLabel>Conversion Tracking</FormLabel>
                    <FormDescription>
                      Track all interactions, clicks, and conversions for this calendar
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="google_meet_enabled"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                  <div className="space-y-0.5">
                    <FormLabel>Google Meet Integration</FormLabel>
                    <FormDescription>
                      Enable Google Meet links for this calendar
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {form.watch('google_meet_enabled') && (
              <FormField
                control={form.control}
                name="google_meet_auto_generate"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                    <div className="space-y-0.5">
                      <FormLabel>Auto-Generate Meet Links</FormLabel>
                      <FormDescription>
                        Automatically create Google Meet links for new bookings
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            )}

            <FormField
              control={form.control}
              name="whats_next_enabled"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                  <div className="space-y-0.5">
                    <FormLabel>"What's Next" Section</FormLabel>
                    <FormDescription>
                      Show visitors what happens after they book
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            {form.watch('whats_next_enabled') && (
              <>
                <FormField
                  control={form.control}
                  name="whats_next_title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>What's Next Title</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., What happens next?" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="whats_next_description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>What's Next Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Explain what happens after booking..."
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}

            <div className="flex justify-end space-x-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading}>
                {isLoading ? 'Creating...' : 'Create Calendar'}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};