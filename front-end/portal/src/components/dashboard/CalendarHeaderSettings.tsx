import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { ImageUpload } from '@/components/index/ImageUpload';
import { Eye, ImageIcon } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface CalendarHeaderSettingsProps {
  calendarId: string;
  onSettingsUpdated?: () => void;
}

interface HeaderSettings {
  header_title: string;
  header_description: string;
  header_image_url: string;
  show_header: boolean;
}

export const CalendarHeaderSettings = ({ calendarId, onSettingsUpdated }: CalendarHeaderSettingsProps) => {
  const [settings, setSettings] = useState<HeaderSettings>({
    header_title: "Book Your Session",
    header_description: "Choose a convenient time that works for you",
    header_image_url: "",
    show_header: true
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (calendarId) {
      loadSettings();
    }
  }, [calendarId]);

  const loadSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('calendars')
        .select('header_title, header_description, header_image_url, show_header')
        .eq('id', calendarId)
        .single();

      if (error) {
        console.error('Error loading calendar settings:', error);
      } else if (data) {
        setSettings({
          header_title: data.header_title || "Book Your Session",
          header_description: data.header_description || "Choose a convenient time that works for you",
          header_image_url: data.header_image_url || "",
          show_header: data.show_header ?? true
        });
      }
    } catch (error) {
      console.error('Error loading calendar settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const updateHeaderVisibility = async (showHeader: boolean) => {
    try {
      const { error } = await supabase
        .from('calendars')
        .update({ show_header: showHeader })
        .eq('id', calendarId);

      if (error) {
        toast({
          title: 'Error',
          description: error.message,
          variant: 'destructive',
        });
      } else {
        setSettings(prev => ({ ...prev, show_header: showHeader }));
        toast({
          title: 'Header Setting Updated!',
          description: `Header is now ${showHeader ? 'shown' : 'hidden'} for this calendar.`,
        });
        onSettingsUpdated?.();
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      });
    }
  };

  const saveHeaderContent = async () => {
    setIsSaving(true);
    try {
      const { error } = await supabase
        .from('calendars')
        .update({
          header_title: settings.header_title,
          header_description: settings.header_description,
          header_image_url: settings.header_image_url || null
        })
        .eq('id', calendarId);

      if (error) {
        toast({
          title: 'Error',
          description: error.message,
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'Header Content Saved!',
          description: 'Your calendar header has been updated.',
        });
        onSettingsUpdated?.();
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold mb-2 flex items-center">
          <Eye className="w-5 h-5 mr-2" />
          Header Settings
        </h3>
        <p className="text-muted-foreground">
          Customize the header section for this specific calendar
        </p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-base">
            <Eye className="w-4 h-4 mr-2" />
            Header Display
          </CardTitle>
          <CardDescription>
            Control whether to show the header section for this calendar
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Header Visibility Toggle */}
          <div className="flex items-center space-x-2">
            <Switch
              id="show-header"
              checked={settings.show_header}
              onCheckedChange={updateHeaderVisibility}
            />
            <Label htmlFor="show-header">
              {settings.show_header ? 'Show header on this calendar' : 'Hide header on this calendar'}
            </Label>
          </div>

          {/* Header Customization - only show if header is enabled */}
          {settings.show_header && (
            <div className="space-y-4 pt-4 border-t">
              <h4 className="font-medium text-sm">Header Content</h4>
              
              <div className="space-y-3">
                <div>
                  <Label htmlFor="header-title">Header Title</Label>
                  <Input
                    id="header-title"
                    value={settings.header_title}
                    onChange={(e) => setSettings(prev => ({ ...prev, header_title: e.target.value }))}
                    placeholder="Book Your Session"
                  />
                </div>

                <div>
                  <Label htmlFor="header-description">Header Description</Label>
                  <Textarea
                    id="header-description"
                    value={settings.header_description}
                    onChange={(e) => setSettings(prev => ({ ...prev, header_description: e.target.value }))}
                    placeholder="Choose a convenient time that works for you"
                    rows={3}
                  />
                </div>

                <div>
                  <ImageUpload
                    currentImageUrl={settings.header_image_url}
                    onImageUpload={(url) => setSettings(prev => ({ ...prev, header_image_url: url }))}
                    onImageRemove={() => setSettings(prev => ({ ...prev, header_image_url: "" }))}
                    label="Header Background Image"
                    bucketName="header-images"
                    folder="headers"
                  />
                  <p className="text-xs text-muted-foreground mt-2">
                    Upload a background image to make your header more engaging. Recommended size: 1200x400px
                  </p>
                </div>

                <div>
                  <Label htmlFor="header-image">Or paste image URL</Label>
                  <Input
                    id="header-image"
                    value={settings.header_image_url}
                    onChange={(e) => setSettings(prev => ({ ...prev, header_image_url: e.target.value }))}
                    placeholder="https://example.com/your-header-image.jpg"
                  />
                </div>

                <Button
                  onClick={saveHeaderContent}
                  disabled={isSaving}
                  className="w-full"
                >
                  {isSaving ? 'Saving...' : 'Save Header Content'}
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};