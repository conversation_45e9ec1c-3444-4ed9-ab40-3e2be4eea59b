import { useAuth } from '@/hooks/useAuth';
import { But<PERSON> } from '@/components/ui/button';
import { Calendar, LogOut, BarChart3, Apple, User, Settings } from 'lucide-react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { DashboardSidebar } from './DashboardSidebar';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

export const DashboardLayout = ({ children }: DashboardLayoutProps) => {
  const { user, signOut } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  // Determine active tab based on current route
  const getActiveTab = () => {
    const path = location.pathname;
    if (path === '/index') return 'overview';
    if (path === '/index/analytics') return 'analytics';
    if (path === '/index/google-calendar') return 'google-calendar';
    if (path === '/index/apple-calendar') return 'apple-calendar';
    return 'overview';
  };

  // Handle tab changes with proper navigation
  const handleTabChange = (tab: string) => {
    switch (tab) {
      case 'overview':
        navigate('/dashboard');
        break;
      case 'user-settings':
      case 'account':
      case 'availability':
      case 'headers':
        // These are handled in Dashboard component, stay on main index
        navigate('/dashboard');
        break;
      default:
        navigate('/dashboard');
    }
  };

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-gradient-to-br from-background via-background to-secondary/20">
        {/* Header */}
        <header className="fixed top-0 left-0 right-0 h-16 bg-white/80 backdrop-blur-sm border-b border-border/50 shadow-elegant z-50">
          <div className="flex items-center justify-between h-full px-4">
            <div className="flex items-center space-x-4">
              <SidebarTrigger className="lg:hidden" />
              <Link to="/dashboard" className="font-bold text-xl bg-gradient-primary bg-clip-text text-transparent">
                BookEasy
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-10 h-10 rounded-full bg-primary hover:bg-primary/90 p-0"
                  >
                    <User className="w-5 h-5 text-primary-foreground" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-48 p-2" align="end">
                  <div className="space-y-1">
                    <div className="px-2 py-1.5 text-sm text-muted-foreground border-b border-border/50 mb-2">
                      {user?.email}
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start text-left"
                      onClick={() => navigate('/user-settings')}
                    >
                      <Settings className="w-4 h-4 mr-2" />
                      User Settings
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start text-left text-destructive hover:text-destructive"
                      onClick={signOut}
                    >
                      <LogOut className="w-4 h-4 mr-2" />
                      Sign Out
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </header>

        {/* Sidebar */}
        <DashboardSidebar
          selectedCalendar={null}
          activeTab={getActiveTab()}
          onTabChange={handleTabChange}
        />

        {/* Main Content */}
        <main className="flex-1 pt-16 overflow-auto">
          <div className="p-6">
            {children}
          </div>
        </main>
      </div>
    </SidebarProvider>
  );
};