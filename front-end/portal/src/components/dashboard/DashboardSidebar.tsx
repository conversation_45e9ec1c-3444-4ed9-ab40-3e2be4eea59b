import { useState } from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarTrigger,
  useSidebar,
} from '@/components/ui/sidebar';

import { 
  LayoutDashboard, 
  Calendar, 
  Palette, 
  DollarSign, 
  Settings,
  Eye,
  BarChart3,
  Apple,
  User,
  Users,
  CalendarDays,
  Monitor,
  Clock,
  Target,
  Star
} from 'lucide-react';

interface DashboardSidebarProps {
  selectedCalendar: string | null;
  activeTab: string;
  onTabChange: (tab: string) => void;
}

const accountLevelNavigation = [
  { title: 'Calendars', value: 'overview', icon: LayoutDashboard },
  { title: 'Account Settings', value: 'account', icon: Settings },
];

const calendarLevelNavigation = [
  { title: 'Visibility', value: 'visibility', icon: Eye },
  { title: 'Header Settings', value: 'headers', icon: Monitor },
  { title: 'UTM Settings', value: 'utm', icon: Target },
  { title: 'Thank You Page', value: 'thankyou', icon: Star },
  { title: 'Data Layer Settings', value: 'datalayer', icon: BarChart3 },
];

const externalNavigation = [
  { title: 'Analytics', href: '/index/analytics', icon: BarChart3 },
  { title: 'Meetings', href: '/index/meetings', icon: CalendarDays },
  { title: 'Contacts', href: '/index/contacts', icon: Users },
  { title: 'Availability', href: '/availability', icon: Clock },
  { title: 'Google Calendar', href: '/index/google-calendar', icon: Calendar },
  { title: 'Apple Calendar', href: '/index/apple-calendar', icon: Apple },
];

export function DashboardSidebar({ selectedCalendar, activeTab, onTabChange }: DashboardSidebarProps) {
  const { state } = useSidebar();
  const location = useLocation();

  const isActive = (value: string) => activeTab === value;

  return (
    <Sidebar className={state === "collapsed" ? "w-14" : "w-80"} collapsible="icon">
      <SidebarTrigger className="m-2 self-end" />
      
      <SidebarContent className="pt-8">
        <SidebarGroup>
          <SidebarGroupLabel className="flex items-center gap-2">
            <LayoutDashboard className="w-4 h-4" />
            {state !== "collapsed" && "Account"}
          </SidebarGroupLabel>
          
          <SidebarGroupContent>
            <SidebarMenu>
              {accountLevelNavigation.map((item) => (
                <SidebarMenuItem key={item.value}>
                  <SidebarMenuButton
                    onClick={() => onTabChange(item.value)}
                    className={isActive(item.value) ? "bg-muted text-primary font-medium" : "hover:bg-muted/50"}
                  >
                    <item.icon className="w-4 h-4" />
                    {state !== "collapsed" && <span>{item.title}</span>}
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

        {selectedCalendar && (
          <SidebarGroup>
            <SidebarGroupLabel className="flex items-center gap-2">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: '#6366f1' }}
              />
              {state !== "collapsed" && "Calendar Settings"}
            </SidebarGroupLabel>
            
            <SidebarGroupContent>
              <SidebarMenu>
                {calendarLevelNavigation.map((item) => (
                  <SidebarMenuItem key={item.value}>
                    <SidebarMenuButton
                      onClick={() => onTabChange(item.value)}
                      className={`
                        ${isActive(item.value) ? "bg-muted text-primary font-medium" : "hover:bg-muted/50"}
                        ml-2
                      `}
                    >
                      <item.icon className="w-4 h-4 text-muted-foreground" />
                      {state !== "collapsed" && (
                        <span className="text-sm">
                          {item.title}
                        </span>
                      )}
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        )}

        <SidebarGroup>
          <SidebarGroupLabel className="flex items-center gap-2">
            <Clock className="w-4 h-4" />
            {state !== "collapsed" && "Tools"}
          </SidebarGroupLabel>
          
          <SidebarGroupContent>
            <SidebarMenu>
              {externalNavigation.map((item) => (
                <SidebarMenuItem key={item.href}>
                  <SidebarMenuButton asChild>
                    <NavLink 
                      to={item.href}
                      className={({ isActive }) => 
                        isActive ? "bg-muted text-primary font-medium" : "hover:bg-muted/50"
                      }
                    >
                      <item.icon className="w-4 h-4" />
                      {state !== "collapsed" && <span>{item.title}</span>}
                    </NavLink>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>

      </SidebarContent>
    </Sidebar>
  );
}