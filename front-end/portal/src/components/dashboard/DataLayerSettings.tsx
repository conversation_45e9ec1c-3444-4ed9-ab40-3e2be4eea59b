import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { BarChart3, Plus, X, Code, Info, Eye } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface DataLayerSettingsProps {
  calendarId: string;
}

interface DataLayerConfig {
  enabled: boolean;
  events: {
    page_view: {
      enabled: boolean;
      event_name: string;
      custom_parameters: { key: string; value: string }[];
    };
    booking_started: {
      enabled: boolean;
      event_name: string;
      custom_parameters: { key: string; value: string }[];
    };
    booking_completed: {
      enabled: boolean;
      event_name: string;
      custom_parameters: { key: string; value: string }[];
    };
    form_interaction: {
      enabled: boolean;
      event_name: string;
      custom_parameters: { key: string; value: string }[];
    };
  };
  utm_collection: {
    enabled: boolean;
    collect_from_query: boolean;
    collect_from_slug: boolean;
    parameters: Array<{
      name: string;
      enabled: boolean;
    }>;
  };
  hidden_fields: {
    enabled: boolean;
    fields: Array<{
      name: string;
      value: string;
      description: string;
    }>;
  };
  custom_events: Array<{
    name: string;
    trigger: string;
    event_name: string;
    parameters: { key: string; value: string }[];
  }>;
  enhanced_ecommerce: {
    enabled: boolean;
    currency: string;
    item_category: string;
    item_name: string;
    value: string;
  };
}

const defaultConfig: DataLayerConfig = {
  enabled: false,
  events: {
    page_view: {
      enabled: true,
      event_name: 'page_view',
      custom_parameters: []
    },
    booking_started: {
      enabled: true,
      event_name: 'begin_booking',
      custom_parameters: []
    },
    booking_completed: {
      enabled: true,
      event_name: 'purchase',
      custom_parameters: []
    },
    form_interaction: {
      enabled: true,
      event_name: 'form_interaction',
      custom_parameters: []
    }
  },
  utm_collection: {
    enabled: false,
    collect_from_query: true,
    collect_from_slug: false,
    parameters: [
      { name: 'utm_source', enabled: true },
      { name: 'utm_medium', enabled: true },
      { name: 'utm_campaign', enabled: true },
      { name: 'utm_term', enabled: false },
      { name: 'utm_content', enabled: false }
    ]
  },
  hidden_fields: {
    enabled: false,
    fields: []
  },
  custom_events: [],
  enhanced_ecommerce: {
    enabled: false,
    currency: 'USD',
    item_category: 'Booking',
    item_name: 'Appointment',
    value: '0'
  }
};

export function DataLayerSettings({ calendarId }: DataLayerSettingsProps) {
  const [config, setConfig] = useState<DataLayerConfig>(defaultConfig);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadDataLayerSettings();
  }, [calendarId]);

  const loadDataLayerSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('calendars')
        .select('data_layer_config')
        .eq('id', calendarId)
        .single();

      if (error) {
        console.error('Error loading data layer settings:', error);
      } else {
        const dataLayerConfig = (data as any)?.data_layer_config || {};
        setConfig({
          ...defaultConfig,
          ...dataLayerConfig,
          events: {
            ...defaultConfig.events,
            ...dataLayerConfig.events
          },
          utm_collection: {
            ...defaultConfig.utm_collection,
            ...dataLayerConfig.utm_collection
          },
          hidden_fields: {
            ...defaultConfig.hidden_fields,
            ...dataLayerConfig.hidden_fields
          },
          enhanced_ecommerce: {
            ...defaultConfig.enhanced_ecommerce,
            ...dataLayerConfig.enhanced_ecommerce
          }
        });
      }
    } catch (error) {
      console.error('Error loading data layer settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveDataLayerSettings = async () => {
    setIsSaving(true);
    try {
      const { error } = await supabase
        .from('calendars')
        .update({ data_layer_config: config } as any)
        .eq('id', calendarId);

      if (error) {
        toast({
          title: 'Error',
          description: 'Failed to save data layer settings. Please try again.',
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'Settings saved',
          description: 'Data layer tracking settings have been updated successfully.',
        });
      }
    } catch (error) {
      console.error('Error saving data layer settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to save data layer settings. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const updateEventConfig = (eventType: keyof DataLayerConfig['events'], field: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      events: {
        ...prev.events,
        [eventType]: {
          ...prev.events[eventType],
          [field]: value
        }
      }
    }));
  };

  const addCustomParameter = (eventType: keyof DataLayerConfig['events']) => {
    updateEventConfig(eventType, 'custom_parameters', [
      ...config.events[eventType].custom_parameters,
      { key: '', value: '' }
    ]);
  };

  const removeCustomParameter = (eventType: keyof DataLayerConfig['events'], index: number) => {
    updateEventConfig(eventType, 'custom_parameters', 
      config.events[eventType].custom_parameters.filter((_, i) => i !== index)
    );
  };

  const updateCustomParameter = (eventType: keyof DataLayerConfig['events'], index: number, key: string, value: string) => {
    const updatedParams = [...config.events[eventType].custom_parameters];
    updatedParams[index] = { key, value };
    updateEventConfig(eventType, 'custom_parameters', updatedParams);
  };

  const addCustomEvent = () => {
    setConfig(prev => ({
      ...prev,
      custom_events: [...prev.custom_events, {
        name: '',
        trigger: 'page_load',
        event_name: '',
        parameters: []
      }]
    }));
  };

  const removeCustomEvent = (index: number) => {
    setConfig(prev => ({
      ...prev,
      custom_events: prev.custom_events.filter((_, i) => i !== index)
    }));
  };

  const addHiddenField = () => {
    setConfig(prev => ({
      ...prev,
      hidden_fields: {
        ...prev.hidden_fields,
        fields: [...prev.hidden_fields.fields, {
          name: '',
          value: '',
          description: ''
        }]
      }
    }));
  };

  const removeHiddenField = (index: number) => {
    setConfig(prev => ({
      ...prev,
      hidden_fields: {
        ...prev.hidden_fields,
        fields: prev.hidden_fields.fields.filter((_, i) => i !== index)
      }
    }));
  };

  const updateHiddenField = (index: number, field: string, value: string) => {
    setConfig(prev => ({
      ...prev,
      hidden_fields: {
        ...prev.hidden_fields,
        fields: prev.hidden_fields.fields.map((hiddenField, i) => 
          i === index ? { ...hiddenField, [field]: value } : hiddenField
        )
      }
    }));
  };

  const generatePreviewCode = () => {
    if (!config.enabled) return '';

    const hiddenFieldsHTML = config.hidden_fields.enabled && config.hidden_fields.fields.length > 0 
      ? `<!-- Hidden Fields for Tracking -->
${config.hidden_fields.fields.map(field => 
  `<input type="hidden" name="${field.name}" value="${field.value}" data-description="${field.description}" />`
).join('\n')}

` : '';

    const utmCollectionCode = config.utm_collection.enabled ? `
// UTM Parameter Collection
function getUTMParameters() {
  const utmParams = {};
  const enabledParams = ${JSON.stringify(config.utm_collection.parameters.filter(p => p.enabled).map(p => p.name))};
  
  ${config.utm_collection.collect_from_query ? `// Collect from query parameters
  const urlParams = new URLSearchParams(window.location.search);
  enabledParams.forEach(param => {
    const value = urlParams.get(param);
    if (value) utmParams[param] = value;
  });` : ''}
  
  ${config.utm_collection.collect_from_slug ? `// Collect from URL slug/path
  const pathSegments = window.location.pathname.split('/');
  pathSegments.forEach(segment => {
    enabledParams.forEach(param => {
      const match = segment.match(new RegExp(param + '=([^&/]+)'));
      if (match) utmParams[param] = decodeURIComponent(match[1]);
    });
  });` : ''}
  
  return utmParams;
}

const utmData = getUTMParameters();
` : '';

    const events = Object.entries(config.events)
      .filter(([_, eventConfig]) => eventConfig.enabled)
      .map(([eventType, eventConfig]) => {
        const customParams = eventConfig.custom_parameters
          .filter(param => param.key && param.value)
          .reduce((acc, param) => ({ ...acc, [param.key]: param.value }), {});

        const hiddenFieldsData = config.hidden_fields.enabled 
          ? config.hidden_fields.fields.reduce((acc, field) => ({ ...acc, [field.name]: field.value }), {})
          : {};

        const utmInclude = config.utm_collection.enabled ? '...utmData,' : '';

        return `// ${eventType.replace('_', ' ').toUpperCase()} Event
dataLayer.push({
  'event': '${eventConfig.event_name}',
  'calendar_id': '${calendarId}',
  'event_category': 'booking',
  ${utmInclude}
  ...${JSON.stringify({ ...customParams, ...hiddenFieldsData }, null, 2)}
});`;
      }).join('\n\n');

    return `<!-- Data Layer Events Configuration -->
${hiddenFieldsHTML}<script>
// Note: GTM ContainerComponent ID should be configured in Analytics settings
${utmCollectionCode}
${events}

${config.enhanced_ecommerce.enabled ? `
// Enhanced Ecommerce Example
dataLayer.push({
  'event': 'purchase',
  'ecommerce': {
    'currency': '${config.enhanced_ecommerce.currency}',
    'value': '${config.enhanced_ecommerce.value}',
    'items': [{
      'item_id': 'booking_${calendarId}',
      'item_name': '${config.enhanced_ecommerce.item_name}',
      'item_category': '${config.enhanced_ecommerce.item_category}',
      'price': '${config.enhanced_ecommerce.value}',
      'quantity': 1
    }]
  },
  ...utmData
});` : ''}
</script>`;
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                Data Layer & GTM Settings
              </CardTitle>
              <CardDescription>
                Configure Google Tag Manager and data layer events for advanced conversion tracking and analytics.
              </CardDescription>
            </div>
            <Button 
              variant="outline" 
              onClick={() => setShowPreview(!showPreview)}
            >
              <Eye className="w-4 h-4 mr-2" />
              {showPreview ? 'Hide' : 'Show'} Code Preview
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="data-layer-enabled">Enable Data Layer Tracking</Label>
              <p className="text-sm text-muted-foreground">
                Push events to Google Tag Manager data layer
              </p>
            </div>
            <Switch
              id="data-layer-enabled"
              checked={config.enabled}
              onCheckedChange={(checked) => setConfig(prev => ({ ...prev, enabled: checked }))}
            />
          </div>

          {config.enabled && (
            <>
              <Separator />

              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  Make sure to configure your Google Tag Manager Container ID in the Analytics section for these events to work properly.
                </AlertDescription>
              </Alert>

              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4">Standard Events</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Configure automatic data layer events for common booking interactions
                  </p>
                </div>

                {Object.entries(config.events).map(([eventType, eventConfig]) => (
                  <Card key={eventType} className="p-4">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium capitalize">
                            {eventType.replace('_', ' ')} Event
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            {eventType === 'page_view' && 'Triggered when the booking page loads'}
                            {eventType === 'booking_started' && 'Triggered when user starts the booking process'}
                            {eventType === 'booking_completed' && 'Triggered when booking is successfully completed'}
                            {eventType === 'form_interaction' && 'Triggered when user interacts with booking form'}
                          </p>
                        </div>
                        <Switch
                          checked={eventConfig.enabled}
                          onCheckedChange={(checked) => updateEventConfig(eventType as keyof DataLayerConfig['events'], 'enabled', checked)}
                        />
                      </div>

                      {eventConfig.enabled && (
                        <div className="space-y-4 pl-4 border-l-2 border-muted">
                          <div className="space-y-2">
                            <Label>Event Name</Label>
                            <Input
                              value={eventConfig.event_name}
                              onChange={(e) => updateEventConfig(eventType as keyof DataLayerConfig['events'], 'event_name', e.target.value)}
                              placeholder="event_name"
                            />
                          </div>

                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <Label>Custom Parameters</Label>
                              <Button 
                                variant="outline" 
                                size="sm" 
                                onClick={() => addCustomParameter(eventType as keyof DataLayerConfig['events'])}
                              >
                                <Plus className="w-4 h-4 mr-2" />
                                Add Parameter
                              </Button>
                            </div>

                            {eventConfig.custom_parameters.map((param, index) => (
                              <div key={index} className="flex gap-2 items-end">
                                <div className="flex-1">
                                  <Input
                                    placeholder="parameter_key"
                                    value={param.key}
                                    onChange={(e) => updateCustomParameter(eventType as keyof DataLayerConfig['events'], index, e.target.value, param.value)}
                                  />
                                </div>
                                <div className="flex-1">
                                  <Input
                                    placeholder="parameter_value"
                                    value={param.value}
                                    onChange={(e) => updateCustomParameter(eventType as keyof DataLayerConfig['events'], index, param.key, e.target.value)}
                                  />
                                </div>
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => removeCustomParameter(eventType as keyof DataLayerConfig['events'], index)}
                                >
                                  <X className="w-4 h-4" />
                                </Button>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </Card>
                ))}
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>UTM Parameter Collection</Label>
                    <p className="text-sm text-muted-foreground">
                      Automatically collect UTM parameters from URL query strings and/or URL slugs
                    </p>
                  </div>
                  <Switch
                    checked={config.utm_collection.enabled}
                    onCheckedChange={(checked) => setConfig(prev => ({
                      ...prev,
                      utm_collection: { ...prev.utm_collection, enabled: checked }
                    }))}
                  />
                </div>

                {config.utm_collection.enabled && (
                  <div className="space-y-4 pl-4 border-l-2 border-muted">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={config.utm_collection.collect_from_query}
                          onCheckedChange={(checked) => setConfig(prev => ({
                            ...prev,
                            utm_collection: { ...prev.utm_collection, collect_from_query: checked }
                          }))}
                        />
                        <div>
                          <Label>From Query Parameters</Label>
                          <p className="text-xs text-muted-foreground">?utm_source=google&utm_medium=cpc</p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Switch
                          checked={config.utm_collection.collect_from_slug}
                          onCheckedChange={(checked) => setConfig(prev => ({
                            ...prev,
                            utm_collection: { ...prev.utm_collection, collect_from_slug: checked }
                          }))}
                        />
                        <div>
                          <Label>From URL Slug</Label>
                          <p className="text-xs text-muted-foreground">/book/id/utm_source=google/utm_medium=cpc</p>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>UTM Parameters to Collect</Label>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                        {config.utm_collection.parameters.map((param, index) => (
                          <div key={param.name} className="flex items-center space-x-2">
                            <Switch
                              checked={param.enabled}
                              onCheckedChange={(checked) => setConfig(prev => ({
                                ...prev,
                                utm_collection: {
                                  ...prev.utm_collection,
                                  parameters: prev.utm_collection.parameters.map((p, i) => 
                                    i === index ? { ...p, enabled: checked } : p
                                  )
                                }
                              }))}
                            />
                            <Label className="text-sm">{param.name}</Label>
                          </div>
                        ))}
                      </div>
                    </div>

                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertDescription>
                        UTM parameters will be automatically included in all data layer events when enabled. 
                        Slug collection looks for patterns like "utm_source=value" in the URL path segments.
                      </AlertDescription>
                    </Alert>
                  </div>
                )}
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Hidden Fields</Label>
                    <p className="text-sm text-muted-foreground">
                      Add invisible form fields that get passed to the data layer for tracking
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Switch
                      checked={config.hidden_fields.enabled}
                      onCheckedChange={(checked) => setConfig(prev => ({
                        ...prev,
                        hidden_fields: { ...prev.hidden_fields, enabled: checked }
                      }))}
                    />
                    {config.hidden_fields.enabled && (
                      <Button variant="outline" size="sm" onClick={addHiddenField}>
                        <Plus className="w-4 h-4 mr-2" />
                        Add Field
                      </Button>
                    )}
                  </div>
                </div>

                {config.hidden_fields.enabled && (
                  <div className="space-y-4 pl-4 border-l-2 border-muted">
                    {config.hidden_fields.fields.length === 0 && (
                      <Alert>
                        <Info className="h-4 w-4" />
                        <AlertDescription>
                          No hidden fields configured. Click "Add Field" to create tracking fields that will be automatically included in your booking forms.
                        </AlertDescription>
                      </Alert>
                    )}
                    
                    {config.hidden_fields.fields.map((field, index) => (
                      <Card key={index} className="p-4">
                        <div className="flex justify-between items-start mb-4">
                          <h4 className="font-medium">Hidden Field #{index + 1}</h4>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => removeHiddenField(index)}
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label>Field Name</Label>
                            <Input
                              placeholder="utm_source"
                              value={field.name}
                              onChange={(e) => updateHiddenField(index, 'name', e.target.value)}
                            />
                            <p className="text-xs text-muted-foreground">This will be the input field name</p>
                          </div>
                          <div className="space-y-2">
                            <Label>Default Value</Label>
                            <Input
                              placeholder="facebook"
                              value={field.value}
                              onChange={(e) => updateHiddenField(index, 'value', e.target.value)}
                            />
                            <p className="text-xs text-muted-foreground">Default value if not populated by URL params</p>
                          </div>
                          <div className="md:col-span-2 space-y-2">
                            <Label>Description</Label>
                            <Input
                              placeholder="Traffic source for campaign tracking"
                              value={field.description}
                              onChange={(e) => updateHiddenField(index, 'description', e.target.value)}
                            />
                            <p className="text-xs text-muted-foreground">Internal description for your reference</p>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                )}
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Enhanced Ecommerce</Label>
                    <p className="text-sm text-muted-foreground">
                      Send booking data as ecommerce events for revenue tracking
                    </p>
                  </div>
                  <Switch
                    checked={config.enhanced_ecommerce.enabled}
                    onCheckedChange={(checked) => setConfig(prev => ({
                      ...prev,
                      enhanced_ecommerce: { ...prev.enhanced_ecommerce, enabled: checked }
                    }))}
                  />
                </div>

                {config.enhanced_ecommerce.enabled && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pl-4 border-l-2 border-muted">
                    <div className="space-y-2">
                      <Label>Currency</Label>
                      <Select 
                        value={config.enhanced_ecommerce.currency}
                        onValueChange={(value) => setConfig(prev => ({
                          ...prev,
                          enhanced_ecommerce: { ...prev.enhanced_ecommerce, currency: value }
                        }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="USD">USD</SelectItem>
                          <SelectItem value="EUR">EUR</SelectItem>
                          <SelectItem value="GBP">GBP</SelectItem>
                          <SelectItem value="CAD">CAD</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label>Item Category</Label>
                      <Input
                        value={config.enhanced_ecommerce.item_category}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          enhanced_ecommerce: { ...prev.enhanced_ecommerce, item_category: e.target.value }
                        }))}
                        placeholder="Booking"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Item Name</Label>
                      <Input
                        value={config.enhanced_ecommerce.item_name}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          enhanced_ecommerce: { ...prev.enhanced_ecommerce, item_name: e.target.value }
                        }))}
                        placeholder="Appointment"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Default Value</Label>
                      <Input
                        value={config.enhanced_ecommerce.value}
                        onChange={(e) => setConfig(prev => ({
                          ...prev,
                          enhanced_ecommerce: { ...prev.enhanced_ecommerce, value: e.target.value }
                        }))}
                        placeholder="0"
                      />
                    </div>
                  </div>
                )}
              </div>

              {showPreview && (
                <>
                  <Separator />
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <Code className="w-5 h-5" />
                      <Label>Generated Code Preview</Label>
                    </div>
                    <div className="bg-muted p-4 rounded-lg">
                      <pre className="text-sm whitespace-pre-wrap overflow-x-auto">
                        <code>{generatePreviewCode()}</code>
                      </pre>
                    </div>
                    <Alert>
                      <Info className="h-4 w-4" />
                      <AlertDescription>
                        This code will be automatically injected into your booking pages when data layer tracking is enabled.
                        Events will be pushed to the data layer based on user interactions.
                      </AlertDescription>
                    </Alert>
                  </div>
                </>
              )}
            </>
          )}
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button onClick={saveDataLayerSettings} disabled={isSaving}>
          {isSaving ? 'Saving...' : 'Save Data Layer Settings'}
        </Button>
      </div>
    </div>
  );
}