import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth'; 
import { supabase } from '@/integrations/supabase/client';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Settings, Clock, Plus, ExternalLink } from 'lucide-react';

interface AvailabilitySchedule {
  id: string;
  name: string;
  description: string | null;
  is_default: boolean;
}

export const GlobalAvailabilitySettings = () => {
  const { user } = useAuth();
  const [schedules, setSchedules] = useState<AvailabilitySchedule[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (user) {
      loadSchedules();
    }
  }, [user]);

  const loadSchedules = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('availability_schedules')
        .select('id, name, description, is_default')
        .eq('user_id', user?.id)
        .order('is_default', { ascending: false })
        .order('name');

      if (error) {
        console.error('Error loading schedules:', error);
        return;
      }

      setSchedules(data || []);
    } catch (error) {
      console.error('Error loading schedules:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Clock className="w-6 h-6 text-primary" />
          <h2 className="text-2xl font-bold">Availability Settings</h2>
        </div>
        <Card className="animate-pulse">
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="h-4 bg-muted rounded w-3/4"></div>
              <div className="h-3 bg-muted rounded w-1/2"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Clock className="w-6 h-6 text-primary" />
          <div>
            <h2 className="text-2xl font-bold">Availability Settings</h2>
            <p className="text-muted-foreground">Manage your availability schedules for all calendars</p>
          </div>
        </div>
        <Button 
          onClick={() => window.location.href = '/availability'}
          className="flex items-center gap-2"
        >
          <Settings className="w-4 h-4" />
          Manage Schedules
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Your Availability Schedules</CardTitle>
        </CardHeader>
        <CardContent>
          {schedules.length === 0 ? (
            <div className="text-center py-8">
              <Clock className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Availability Schedules</h3>
              <p className="text-muted-foreground mb-4">
                Create your first availability schedule to control when visitors can book appointments
              </p>
              <Button 
                onClick={() => window.location.href = '/availability'}
                className="flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                Create Schedule
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              <p className="text-sm text-muted-foreground">
                You have {schedules.length} availability schedule{schedules.length !== 1 ? 's' : ''}. 
                These can be assigned to individual calendars in their Visibility settings.
              </p>
              <div className="grid gap-3">
                {schedules.map((schedule) => (
                  <div 
                    key={schedule.id} 
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div>
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{schedule.name}</h3>
                        {schedule.is_default && (
                          <Badge variant="secondary" className="text-xs">
                            Default
                          </Badge>
                        )}
                      </div>
                      {schedule.description && (
                        <p className="text-sm text-muted-foreground mt-1">
                          {schedule.description}
                        </p>
                      )}
                    </div>
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => window.location.href = '/availability'}
                      className="flex items-center gap-1"
                    >
                      <ExternalLink className="w-3 h-3" />
                      Edit
                    </Button>
                  </div>
                ))}
              </div>
              <div className="flex justify-center pt-4">
                <Button 
                  variant="outline"
                  onClick={() => window.location.href = '/availability'}
                  className="flex items-center gap-2"
                >
                  <Settings className="w-4 h-4" />
                  Manage All Schedules
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};