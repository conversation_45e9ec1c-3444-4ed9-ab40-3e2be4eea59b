import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, ExternalLink } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

interface Calendar {
  id: string;
  name: string;
  description: string;
  duration: number;
  color: string;
  is_active: boolean;
  theme_id: string;
  conversion_tracking: boolean;
  google_meet_enabled: boolean;
  google_meet_auto_generate: boolean;
  whats_next_enabled: boolean;
  whats_next_title: string;
  whats_next_description: string;
  header_title: string;
  header_description: string;
  header_image_url: string;
  show_header: boolean;
  created_at: string;
}

interface LivePreviewProps {
  calendarId: string | null;
}

export const LivePreview = ({ calendarId }: LivePreviewProps) => {
  const [calendar, setCalendar] = useState<Calendar | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (calendarId) {
      loadCalendar();
    } else {
      setCalendar(null);
    }
  }, [calendarId]);

  const loadCalendar = async () => {
    if (!calendarId) return;
    
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('calendars')
        .select('*')
        .eq('id', calendarId)
        .single();

      if (!error && data) {
        setCalendar(data);
      }
    } catch (error) {
      console.error('Error loading calendar:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const openBookingPage = () => {
    if (calendar) {
      const link = `${window.location.origin}/booking/${calendar.id}`;
      window.open(link, '_blank');
    }
  };

  if (!calendarId) {
    return (
      <div className="h-full flex items-center justify-center text-center p-6">
        <div>
          <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No Calendar Selected</h3>
          <p className="text-muted-foreground text-sm">
            Select a calendar from the dashboard to see a live preview
          </p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!calendar) {
    return (
      <div className="h-full flex items-center justify-center text-center p-6">
        <div>
          <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Calendar Not Found</h3>
          <p className="text-muted-foreground text-sm">
            The selected calendar could not be loaded
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Preview Header */}
      <div className="p-4 border-b bg-muted/20">
        <div className="flex items-center justify-between mb-2">
          <h3 className="font-semibold text-sm">Live Preview</h3>
          <Button 
            size="sm" 
            variant="outline" 
            onClick={openBookingPage}
            className="text-xs"
          >
            <ExternalLink className="w-3 h-3 mr-1" />
            Open
          </Button>
        </div>
        <p className="text-xs text-muted-foreground">
          How visitors see your booking calendar
        </p>
      </div>

      {/* Preview Content */}
      <div className="flex-1 p-4 bg-gradient-to-br from-background to-muted/20 overflow-auto">
        <div className="max-w-md mx-auto">
          {/* Calendar Card Preview */}
          <Card className="shadow-lg">
            <CardHeader className="text-center">
              <div className="flex items-center justify-center mb-3">
                <div
                  className="w-4 h-4 rounded-full mr-2"
                  style={{ backgroundColor: calendar.color }}
                />
                <CardTitle className="text-lg">{calendar.name}</CardTitle>
              </div>
              {calendar.description && (
                <p className="text-sm text-muted-foreground">{calendar.description}</p>
              )}
            </CardHeader>
            
            <CardContent className="space-y-4">
              {/* Duration Display */}
              <div className="flex items-center justify-center text-sm text-muted-foreground">
                <Clock className="w-4 h-4 mr-2" />
                {calendar.duration} minutes
              </div>

              {/* Calendar Grid Preview */}
              <div className="grid grid-cols-7 gap-1 text-xs">
                <div className="text-center font-medium p-1">S</div>
                <div className="text-center font-medium p-1">M</div>
                <div className="text-center font-medium p-1">T</div>
                <div className="text-center font-medium p-1">W</div>
                <div className="text-center font-medium p-1">T</div>
                <div className="text-center font-medium p-1">F</div>
                <div className="text-center font-medium p-1">S</div>
                
                {/* Sample calendar dates */}
                {Array.from({ length: 35 }, (_, i) => {
                  const day = i - 5; // Start from previous month
                  const isCurrentMonth = day > 0 && day <= 30;
                  const isSelected = day === 15;
                  
                  return (
                    <div
                      key={i}
                      className={`
                        text-center p-1 rounded text-xs
                        ${isCurrentMonth ? 'text-foreground' : 'text-muted-foreground/50'}
                        ${isSelected ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'}
                      `}
                    >
                      {day > 0 && day <= 30 ? day : ''}
                    </div>
                  );
                })}
              </div>

              {/* Sample Time Slots */}
              <div className="space-y-2">
                <p className="text-sm font-medium">Available Times</p>
                <div className="grid grid-cols-2 gap-2">
                  {['9:00 AM', '10:30 AM', '2:00 PM', '3:30 PM'].map((time) => (
                    <Button 
                      key={time} 
                      variant="outline" 
                      size="sm" 
                      className="text-xs"
                      style={{ borderColor: calendar.color }}
                    >
                      {time}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Book Button */}
              <Button 
                className="w-full" 
                style={{ backgroundColor: calendar.color }}
              >
                Book Now
              </Button>

              {/* Status Badges */}
              <div className="flex gap-2 justify-center">
                <Badge variant={calendar.is_active ? 'default' : 'secondary'} className="text-xs">
                  {calendar.is_active ? 'Active' : 'Inactive'}
                </Badge>
                {calendar.conversion_tracking && (
                  <Badge variant="outline" className="text-xs">
                    Tracking
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};