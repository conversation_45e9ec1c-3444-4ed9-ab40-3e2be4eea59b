import { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, ExternalLink, X, Eye, Monitor, Smartphone, Settings } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';

interface Calendar {
  id: string;
  name: string;
  description: string;
  duration: number;
  color: string;
  is_active: boolean;
  theme_id: string;
  conversion_tracking: boolean;
  google_meet_enabled: boolean;
  google_meet_auto_generate: boolean;
  whats_next_enabled: boolean;
  whats_next_title: string;
  whats_next_description: string;
  header_title: string;
  header_description: string;
  header_image_url: string;
  show_header: boolean;
  created_at: string;
}

interface LivePreviewFloatingProps {
  calendarId: string | null;
  isOpen: boolean;
  onClose: () => void;
}

export const LivePreviewFloating = ({ calendarId, isOpen, onClose }: LivePreviewFloatingProps) => {
  const { user } = useAuth();
  const [calendar, setCalendar] = useState<Calendar | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [randomCalendar, setRandomCalendar] = useState<Calendar | null>(null);
  const [previewMode, setPreviewMode] = useState<'desktop' | 'mobile'>('desktop');
  const [userSettings, setUserSettings] = useState<{ 
    show_header: boolean; 
    theme_id?: string; 
  } | null>(null);
  const [currentTheme, setCurrentTheme] = useState<any>(null);
  const [calendarHeaderSettings, setCalendarHeaderSettings] = useState<{
    header_title?: string;
    header_description?: string;
    header_image_url?: string;
    show_header?: boolean;
  } | null>(null);

  useEffect(() => {
    if (isOpen && user) {
      loadUserSettings();
      if (calendarId) {
        loadCalendar();
        setRandomCalendar(null);
      } else {
        loadRandomCalendar();
      }
    } else {
      setCalendar(null);
      setRandomCalendar(null);
    }
  }, [calendarId, isOpen, user]);

  useEffect(() => {
    if (userSettings?.theme_id) {
      loadTheme(userSettings.theme_id);
    }
  }, [userSettings?.theme_id]);

  // Listen for theme changes
  useEffect(() => {
    const handleStorageChange = () => {
      if (userSettings?.theme_id) {
        loadTheme(userSettings.theme_id);
      }
    };

    // Refresh theme when user settings might have changed
    const interval = setInterval(() => {
      if (user && isOpen) {
        loadUserSettings();
      }
    }, 2000);

    return () => {
      clearInterval(interval);
    };
  }, [user, isOpen, userSettings?.theme_id]);

  const loadUserSettings = async () => {
    if (!user) return;
    
    try {
      const { data, error } = await supabase
        .from('user_settings')
        .select('show_header, theme_id')
        .eq('user_id', user.id)
        .single();

      if (!error && data) {
        setUserSettings(data);
      } else {
        // Default settings if none found
        setUserSettings({ show_header: true });
      }
    } catch (error) {
      console.error('Error loading user settings:', error);
      setUserSettings({ show_header: true });
    }
  };

  const loadTheme = async (themeId: string) => {
    try {
      const { data, error } = await supabase
        .from('themes')
        .select('*')
        .eq('id', themeId)
        .single();

      if (!error && data) {
        setCurrentTheme(data);
      }
    } catch (error) {
      console.error('Error loading theme:', error);
    }
  };

  const loadCalendar = async () => {
    if (!calendarId) return;
    
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('calendars')
        .select('*')
        .eq('id', calendarId)
        .single();

      if (!error && data) {
        setCalendar(data);
        setCalendarHeaderSettings({
          header_title: data.header_title,
          header_description: data.header_description,
          header_image_url: data.header_image_url,
          show_header: data.show_header
        });
      }
    } catch (error) {
      console.error('Error loading calendar:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadRandomCalendar = async () => {
    if (!user) return;
    
    setIsLoading(true);
    try {
      const { data, error } = await supabase
        .from('calendars')
        .select('*')
        .eq('user_id', user.id)
        .eq('is_active', true);

      if (!error && data && data.length > 0) {
        const randomIndex = Math.floor(Math.random() * data.length);
        const selectedCalendar = data[randomIndex];
        setRandomCalendar(selectedCalendar);
        setCalendarHeaderSettings({
          header_title: selectedCalendar.header_title,
          header_description: selectedCalendar.header_description,
          header_image_url: selectedCalendar.header_image_url,
          show_header: selectedCalendar.show_header
        });
      } else {
        setRandomCalendar(null);
      }
    } catch (error) {
      console.error('Error loading random calendar:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const openBookingPage = () => {
    if (calendar) {
      const link = `${window.location.origin}/book/${calendar.id}`;
      window.open(link, '_blank');
    }
  };

  // Get colors from theme or fallback to calendar color
  const getPrimaryColor = () => {
    if (currentTheme?.colors?.primary) {
      return currentTheme.colors.primary;
    }
    return (calendar || randomCalendar)?.color || '#6366f1';
  };

  const getAccentColor = () => {
    if (currentTheme?.colors?.accent) {
      return currentTheme.colors.accent;
    }
    return (calendar || randomCalendar)?.color || '#6366f1';
  };

  if (!isOpen) return null;

  return (
    <div className={`fixed top-0 right-0 h-full bg-background border-l shadow-xl z-50 flex flex-col ${previewMode === 'mobile' ? 'w-80' : 'w-96'}`}>
      {/* Header */}
      <div className="p-4 border-b bg-muted/20">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <Eye className="w-5 h-5 text-primary" />
            <h3 className="font-semibold">Live Preview</h3>
          </div>
          <div className="flex items-center gap-2">
            {calendar && (
              <Button 
                size="sm" 
                variant="outline" 
                onClick={openBookingPage}
                className="text-xs"
              >
                <ExternalLink className="w-3 h-3 mr-1" />
                Open
              </Button>
            )}
            <Button size="sm" variant="ghost" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </div>
        
        {/* Preview Controls */}
        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-1 bg-muted rounded-lg p-1">
            <Button
              size="sm"
              variant={previewMode === 'desktop' ? 'default' : 'ghost'}
              onClick={() => setPreviewMode('desktop')}
              className="h-7 px-2"
            >
              <Monitor className="w-3 h-3 mr-1" />
              Desktop
            </Button>
            <Button
              size="sm"
              variant={previewMode === 'mobile' ? 'default' : 'ghost'}
              onClick={() => setPreviewMode('mobile')}
              className="h-7 px-2"
            >
              <Smartphone className="w-3 h-3 mr-1" />
              Mobile
            </Button>
          </div>
          
          <Button
            size="sm"
            variant="outline"
            onClick={async () => {
              const newShowHeader = !calendarHeaderSettings?.show_header;
              try {
                await supabase
                  .from('calendars')
                  .update({ show_header: newShowHeader })
                  .eq('id', calendarId);
                setCalendarHeaderSettings(prev => prev ? { ...prev, show_header: newShowHeader } : { show_header: newShowHeader });
              } catch (error) {
                console.error('Error updating header setting:', error);
              }
            }}
            className="h-7 px-2 text-xs"
          >
            <Settings className="w-3 h-3 mr-1" />
            {calendarHeaderSettings?.show_header ? 'Hide Header' : 'Show Header'}
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto">
        {isLoading ? (
          <div className="h-full flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : (!calendarId && !randomCalendar) ? (
          <div className="h-full flex items-center justify-center text-center p-6">
            <div>
              <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Calendars Found</h3>
              <p className="text-muted-foreground text-sm">
                Create your first calendar to see a live preview
              </p>
            </div>
          </div>
        ) : !calendar && !randomCalendar ? (
          <div className="h-full flex items-center justify-center text-center p-6">
            <div>
              <Calendar className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Calendar Not Found</h3>
              <p className="text-muted-foreground text-sm">
                The selected calendar could not be loaded
              </p>
            </div>
          </div>
        ) : (
          <div className="p-4 bg-gradient-to-br from-background to-muted/20">
            <div className={`mx-auto ${previewMode === 'mobile' ? 'max-w-sm' : 'max-w-full'}`}>
              {/* Optional Header Section */}
              {calendarHeaderSettings?.show_header && (
                <div className="mb-6 text-center">
                  <div 
                    className="bg-gradient-to-r from-primary/10 to-accent/10 rounded-lg p-6 mb-4 relative overflow-hidden"
                    style={calendarHeaderSettings.header_image_url ? {
                      backgroundImage: `url(${calendarHeaderSettings.header_image_url})`,
                      backgroundSize: 'cover',
                      backgroundPosition: 'center',
                    } : {}}
                  >
                    {calendarHeaderSettings.header_image_url && (
                      <div className="absolute inset-0 bg-black/40 rounded-lg"></div>
                    )}
                    <div className="relative z-10">
                      <h1 className={`text-2xl font-bold mb-2 ${calendarHeaderSettings.header_image_url ? 'text-white' : 'text-foreground'}`}>
                        {calendarHeaderSettings.header_title || "Book Your Session"}
                      </h1>
                      <p className={`${calendarHeaderSettings.header_image_url ? 'text-white/90' : 'text-muted-foreground'}`}>
                        {calendarHeaderSettings.header_description || "Choose a convenient time that works for you"}
                      </p>
                    </div>
                  </div>
                </div>
              )}
              
              {/* Calendar Card Preview */}
              <Card className="shadow-lg">
                <CardHeader className="text-center">
                  <div className="flex items-center justify-center mb-3">
                    <div
                      className="w-4 h-4 rounded-full mr-2"
                      style={{ backgroundColor: getPrimaryColor() }}
                    />
                    <CardTitle className={`${previewMode === 'mobile' ? 'text-lg' : 'text-xl'}`}>
                      {(calendar || randomCalendar)?.name}
                    </CardTitle>
                  </div>
                  {(calendar || randomCalendar)?.description && (
                    <p className={`text-muted-foreground ${previewMode === 'mobile' ? 'text-sm' : 'text-base'}`}>
                      {(calendar || randomCalendar)?.description}
                    </p>
                  )}
                </CardHeader>
                
                <CardContent className="space-y-4">
                  {/* Duration Display */}
                  <div className={`flex items-center justify-center text-muted-foreground ${previewMode === 'mobile' ? 'text-sm' : 'text-base'}`}>
                    <Clock className="w-4 h-4 mr-2" />
                    {(calendar || randomCalendar)?.duration} minutes
                  </div>

                  {/* Calendar Grid Preview */}
                  <div className={`grid grid-cols-7 gap-1 ${previewMode === 'mobile' ? 'text-xs' : 'text-sm'}`}>
                    <div className="text-center font-medium p-1">S</div>
                    <div className="text-center font-medium p-1">M</div>
                    <div className="text-center font-medium p-1">T</div>
                    <div className="text-center font-medium p-1">W</div>
                    <div className="text-center font-medium p-1">T</div>
                    <div className="text-center font-medium p-1">F</div>
                    <div className="text-center font-medium p-1">S</div>
                    
                    {/* Sample calendar dates */}
                    {Array.from({ length: 35 }, (_, i) => {
                      const day = i - 5; // Start from previous month
                      const isCurrentMonth = day > 0 && day <= 30;
                      const isSelected = day === 15;
                      
                      return (
                        <div
                          key={i}
                          className={`
                            text-center p-1 rounded cursor-pointer ${previewMode === 'mobile' ? 'text-xs' : 'text-sm'}
                            ${isCurrentMonth ? 'text-foreground' : 'text-muted-foreground/50'}
                            ${isSelected ? 'bg-primary text-primary-foreground' : 'hover:bg-muted'}
                          `}
                        >
                          {day > 0 && day <= 30 ? day : ''}
                        </div>
                      );
                    })}
                  </div>

                  {/* Sample Time Slots */}
                  <div className="space-y-2">
                    <p className={`font-medium ${previewMode === 'mobile' ? 'text-sm' : 'text-base'}`}>
                      Available Times
                    </p>
                    <div className={`grid gap-2 ${previewMode === 'mobile' ? 'grid-cols-1' : 'grid-cols-2'}`}>
                      {['9:00 AM', '10:30 AM', '2:00 PM', '3:30 PM'].map((time) => (
                        <Button 
                          key={time} 
                          variant="outline" 
                          size={previewMode === 'mobile' ? 'sm' : 'default'}
                          className={previewMode === 'mobile' ? 'text-xs' : 'text-sm'}
                          style={{ borderColor: getAccentColor() }}
                        >
                          {time}
                        </Button>
                      ))}
                    </div>
                  </div>

                  {/* Book Button */}
                  <Button 
                    className="w-full" 
                    size={previewMode === 'mobile' ? 'default' : 'lg'}
                    style={{ backgroundColor: getPrimaryColor() }}
                  >
                    Book Now
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};