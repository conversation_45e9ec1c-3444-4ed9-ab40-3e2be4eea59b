import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Star, ExternalLink, Eye, Plus, X } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface ThankYouPageSettingsProps {
  calendarId: string;
}

interface ThankYouConfig {
  enabled: boolean;
  title: string;
  message: string;
  show_booking_details: boolean;
  redirect_enabled: boolean;
  redirect_url: string;
  redirect_delay: number;
  custom_html: string;
  upsells: {
    enabled: boolean;
    items: Array<{
      title: string;
      description: string;
      price: string;
      button_text: string;
      button_url: string;
    }>;
  };
}

export function ThankYouPageSettings({ calendarId }: ThankYouPageSettingsProps) {
  const [config, setConfig] = useState<ThankYouConfig>({
    enabled: true,
    title: 'Thank you for booking!',
    message: 'Your appointment has been confirmed. You will receive a confirmation email shortly with all the details.',
    show_booking_details: true,
    redirect_enabled: false,
    redirect_url: '',
    redirect_delay: 5,
    custom_html: '',
    upsells: {
      enabled: false,
      items: []
    }
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadThankYouSettings();
  }, [calendarId]);

  const loadThankYouSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('calendars')
        .select('thank_you_config')
        .eq('id', calendarId)
        .single();

      if (error) {
        console.error('Error loading thank you settings:', error);
      } else {
        const thankYouConfig = (data as any)?.thank_you_config || {};
        setConfig({
          enabled: thankYouConfig.enabled !== false,
          title: thankYouConfig.title || 'Thank you for booking!',
          message: thankYouConfig.message || 'Your appointment has been confirmed. You will receive a confirmation email shortly with all the details.',
          show_booking_details: thankYouConfig.show_booking_details !== false,
          redirect_enabled: thankYouConfig.redirect_enabled || false,
          redirect_url: thankYouConfig.redirect_url || '',
          redirect_delay: thankYouConfig.redirect_delay || 5,
          custom_html: thankYouConfig.custom_html || '',
          upsells: {
            enabled: thankYouConfig.upsells?.enabled || false,
            items: thankYouConfig.upsells?.items || []
          }
        });
      }
    } catch (error) {
      console.error('Error loading thank you settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveThankYouSettings = async () => {
    setIsSaving(true);
    try {
      const { error } = await supabase
        .from('calendars')
        .update({ thank_you_config: config } as any)
        .eq('id', calendarId);

      if (error) {
        toast({
          title: 'Error',
          description: 'Failed to save thank you page settings. Please try again.',
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'Settings saved',
          description: 'Thank you page settings have been updated successfully.',
        });
      }
    } catch (error) {
      console.error('Error saving thank you settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to save thank you page settings. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const addUpsellItem = () => {
    setConfig(prev => ({
      ...prev,
      upsells: {
        ...prev.upsells,
        items: [...prev.upsells.items, {
          title: '',
          description: '',
          price: '',
          button_text: 'Get This Now',
          button_url: ''
        }]
      }
    }));
  };

  const removeUpsellItem = (index: number) => {
    setConfig(prev => ({
      ...prev,
      upsells: {
        ...prev.upsells,
        items: prev.upsells.items.filter((_, i) => i !== index)
      }
    }));
  };

  const updateUpsellItem = (index: number, field: string, value: string) => {
    setConfig(prev => ({
      ...prev,
      upsells: {
        ...prev.upsells,
        items: prev.upsells.items.map((item, i) => 
          i === index ? { ...item, [field]: value } : item
        )
      }
    }));
  };

  const previewThankYouPage = () => {
    const url = `${window.location.origin}/thank-you/${calendarId}?preview=true`;
    window.open(url, '_blank');
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Star className="w-5 h-5" />
                Thank You Page Settings
              </CardTitle>
              <CardDescription>
                Customize the page visitors see after booking an appointment. Perfect for upsells and additional engagement.
              </CardDescription>
            </div>
            <Button variant="outline" onClick={previewThankYouPage}>
              <Eye className="w-4 h-4 mr-2" />
              Preview
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="thank-you-enabled">Enable Thank You Page</Label>
              <p className="text-sm text-muted-foreground">
                Show a custom thank you page after booking completion
              </p>
            </div>
            <Switch
              id="thank-you-enabled"
              checked={config.enabled}
              onCheckedChange={(checked) => setConfig(prev => ({ ...prev, enabled: checked }))}
            />
          </div>

          {config.enabled && (
            <>
              <Separator />
              
              <div className="grid grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="thank-you-title">Page Title</Label>
                  <Input
                    id="thank-you-title"
                    placeholder="Thank you for booking!"
                    value={config.title}
                    onChange={(e) => setConfig(prev => ({ ...prev, title: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="thank-you-message">Message</Label>
                  <Textarea
                    id="thank-you-message"
                    placeholder="Your appointment has been confirmed..."
                    value={config.message}
                    onChange={(e) => setConfig(prev => ({ ...prev, message: e.target.value }))}
                    rows={3}
                  />
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label htmlFor="show-booking-details">Show Booking Details</Label>
                  <p className="text-sm text-muted-foreground">
                    Display the appointment date, time, and attendee information
                  </p>
                </div>
                <Switch
                  id="show-booking-details"
                  checked={config.show_booking_details}
                  onCheckedChange={(checked) => setConfig(prev => ({ ...prev, show_booking_details: checked }))}
                />
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="redirect-enabled">Auto Redirect</Label>
                    <p className="text-sm text-muted-foreground">
                      Automatically redirect users to another page after a delay
                    </p>
                  </div>
                  <Switch
                    id="redirect-enabled"
                    checked={config.redirect_enabled}
                    onCheckedChange={(checked) => setConfig(prev => ({ ...prev, redirect_enabled: checked }))}
                  />
                </div>

                {config.redirect_enabled && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pl-4 border-l-2 border-muted">
                    <div className="md:col-span-2 space-y-2">
                      <Label htmlFor="redirect-url">Redirect URL</Label>
                      <Input
                        id="redirect-url"
                        placeholder="https://your-website.com/next-page"
                        value={config.redirect_url}
                        onChange={(e) => setConfig(prev => ({ ...prev, redirect_url: e.target.value }))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="redirect-delay">Delay (seconds)</Label>
                      <Input
                        id="redirect-delay"
                        type="number"
                        min="3"
                        max="60"
                        value={config.redirect_delay}
                        onChange={(e) => setConfig(prev => ({ ...prev, redirect_delay: parseInt(e.target.value) || 5 }))}
                      />
                    </div>
                  </div>
                )}
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Upsell Offers</Label>
                    <p className="text-sm text-muted-foreground">
                      Promote additional products or services after booking
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <Switch
                      checked={config.upsells.enabled}
                      onCheckedChange={(checked) => setConfig(prev => ({ 
                        ...prev, 
                        upsells: { ...prev.upsells, enabled: checked }
                      }))}
                    />
                    {config.upsells.enabled && (
                      <Button variant="outline" size="sm" onClick={addUpsellItem}>
                        <Plus className="w-4 h-4 mr-2" />
                        Add Upsell
                      </Button>
                    )}
                  </div>
                </div>

                {config.upsells.enabled && (
                  <div className="space-y-4 pl-4 border-l-2 border-muted">
                    {config.upsells.items.length === 0 && (
                      <Alert>
                        <AlertDescription>
                          No upsell offers configured. Click "Add Upsell" to create your first offer.
                        </AlertDescription>
                      </Alert>
                    )}
                    
                    {config.upsells.items.map((item, index) => (
                      <Card key={index} className="p-4">
                        <div className="flex justify-between items-start mb-4">
                          <h4 className="font-medium">Upsell #{index + 1}</h4>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => removeUpsellItem(index)}
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label>Title</Label>
                            <Input
                              placeholder="Premium Service Upgrade"
                              value={item.title}
                              onChange={(e) => updateUpsellItem(index, 'title', e.target.value)}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>Price</Label>
                            <Input
                              placeholder="$99"
                              value={item.price}
                              onChange={(e) => updateUpsellItem(index, 'price', e.target.value)}
                            />
                          </div>
                          <div className="md:col-span-2 space-y-2">
                            <Label>Description</Label>
                            <Textarea
                              placeholder="Get additional value with our premium service..."
                              value={item.description}
                              onChange={(e) => updateUpsellItem(index, 'description', e.target.value)}
                              rows={2}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>Button Text</Label>
                            <Input
                              placeholder="Get This Now"
                              value={item.button_text}
                              onChange={(e) => updateUpsellItem(index, 'button_text', e.target.value)}
                            />
                          </div>
                          <div className="space-y-2">
                            <Label>Button URL</Label>
                            <Input
                              placeholder="https://your-checkout-url.com"
                              value={item.button_url}
                              onChange={(e) => updateUpsellItem(index, 'button_url', e.target.value)}
                            />
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                )}
              </div>

              <Separator />

              <div className="space-y-2">
                <Label htmlFor="custom-html">Custom HTML/JavaScript</Label>
                <Textarea
                  id="custom-html"
                  placeholder="Add custom tracking codes, chat widgets, or additional HTML..."
                  value={config.custom_html}
                  onChange={(e) => setConfig(prev => ({ ...prev, custom_html: e.target.value }))}
                  rows={4}
                  className="font-mono text-sm"
                />
                <p className="text-xs text-muted-foreground">
                  This HTML will be injected into the thank you page. Perfect for conversion tracking pixels, chat widgets, or custom styling.
                </p>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button onClick={saveThankYouSettings} disabled={isSaving}>
          {isSaving ? 'Saving...' : 'Save Thank You Page Settings'}
        </Button>
      </div>
    </div>
  );
}