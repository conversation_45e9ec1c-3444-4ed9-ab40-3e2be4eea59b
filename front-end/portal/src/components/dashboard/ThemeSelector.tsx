import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Check } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface Theme {
  id: string;
  name: string;
  description: string;
  colors: any; // JSON field from database
  fonts: any; // JSON field from database  
  layout_settings: any; // JSON field from database
  
}

interface ThemeSelectorProps {
  calendarId: string;
  currentThemeId?: string;
  onThemeSelected: (themeId: string) => void;
}

export const ThemeSelector = ({ calendarId, currentThemeId, onThemeSelected }: ThemeSelectorProps) => {
  const [themes, setThemes] = useState<Theme[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadThemes();
  }, []);

  const loadThemes = async () => {
    try {
      const { data, error } = await supabase
        .from('themes')
        .select('*')
        .eq('is_premium', false)
        .order('name');

      if (error) {
        console.error('Error loading themes:', error);
      } else {
        setThemes(data || []);
      }
    } catch (error) {
      console.error('Error loading themes:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const selectTheme = async (themeId: string) => {
    try {
      const { error } = await supabase
        .from('calendars')
        .update({ theme_id: themeId })
        .eq('id', calendarId);

      if (error) {
        toast({
          title: 'Error',
          description: error.message,
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'Theme Updated!',
          description: 'Your calendar theme has been updated successfully.',
        });
        onThemeSelected(themeId);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'An unexpected error occurred',
        variant: 'destructive',
      });
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-semibold mb-2">Choose Your Conversion Theme</h3>
        <p className="text-muted-foreground">
          Select a high-converting theme optimized for maximum bookings and lead generation
        </p>
      </div>
      
      <div className="grid gap-4 md:grid-cols-2">
        {themes.map((theme) => (
          <Card
            key={theme.id}
            className={`cursor-pointer transition-all hover:shadow-lg ${
              currentThemeId === theme.id ? 'ring-2 ring-primary' : ''
            }`}
          >
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center text-base">
                  {theme.name}
                  {currentThemeId === theme.id && (
                    <Check className="w-4 h-4 ml-2 text-green-500" />
                  )}
                </CardTitle>
              </div>
              <CardDescription className="text-xs">
                {theme.description}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {/* Color Preview */}
              <div className="flex space-x-1">
                <div
                  className="w-6 h-6 rounded-full border-2 border-white shadow-sm"
                  style={{ backgroundColor: theme.colors?.primary || '#6366f1' }}
                  title="Primary Color"
                />
                <div
                  className="w-6 h-6 rounded-full border-2 border-white shadow-sm"
                  style={{ backgroundColor: theme.colors?.secondary || '#f8fafc' }}
                  title="Secondary Color"
                />
                <div
                  className="w-6 h-6 rounded-full border-2 border-white shadow-sm"
                  style={{ backgroundColor: theme.colors?.accent || '#0ea5e9' }}
                  title="Accent Color"
                />
              </div>

              {/* Font & Layout Info */}
              <div className="text-xs text-muted-foreground space-y-1">
                <div>Font: {theme.fonts?.heading || 'Default'}</div>
                <div>Layout: {theme.layout_settings?.layout || 'Default'}</div>
                <div>Spacing: {theme.layout_settings?.spacing || 'Default'}</div>
              </div>

              <Button
                variant={currentThemeId === theme.id ? 'default' : 'outline'}
                size="sm"
                className="w-full"
                onClick={() => selectTheme(theme.id)}
                disabled={currentThemeId === theme.id}
              >
                {currentThemeId === theme.id ? 'Current Theme' : 'Select Theme'}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};