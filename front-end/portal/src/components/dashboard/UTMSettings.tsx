import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Target, Plus, X, Info } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface UTMSettingsProps {
  calendarId: string;
}

interface UTMConfig {
  utm_tracking_enabled: boolean;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
  utm_term?: string;
  utm_content?: string;
  custom_parameters?: { key: string; value: string }[];
}

export function UTMSettings({ calendarId }: UTMSettingsProps) {
  const [config, setConfig] = useState<UTMConfig>({
    utm_tracking_enabled: false,
    utm_source: '',
    utm_medium: '',
    utm_campaign: '',
    utm_term: '',
    utm_content: '',
    custom_parameters: []
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const { toast } = useToast();

  useEffect(() => {
    loadUTMSettings();
  }, [calendarId]);

  const loadUTMSettings = async () => {
    try {
      const { data, error } = await supabase
        .from('calendars')
        .select('utm_config')
        .eq('id', calendarId)
        .single();

      if (error) {
        console.error('Error loading UTM settings:', error);
      } else {
        const utmConfig = (data as any)?.utm_config || {};
        setConfig({
          utm_tracking_enabled: utmConfig.utm_tracking_enabled || false,
          utm_source: utmConfig.utm_source || '',
          utm_medium: utmConfig.utm_medium || '',
          utm_campaign: utmConfig.utm_campaign || '',
          utm_term: utmConfig.utm_term || '',
          utm_content: utmConfig.utm_content || '',
          custom_parameters: utmConfig.custom_parameters || []
        });
      }
    } catch (error) {
      console.error('Error loading UTM settings:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveUTMSettings = async () => {
    setIsSaving(true);
    try {
      const { error } = await supabase
        .from('calendars')
        .update({ utm_config: config } as any)
        .eq('id', calendarId);

      if (error) {
        toast({
          title: 'Error',
          description: 'Failed to save UTM settings. Please try again.',
          variant: 'destructive',
        });
      } else {
        toast({
          title: 'Settings saved',
          description: 'UTM tracking settings have been updated successfully.',
        });
      }
    } catch (error) {
      console.error('Error saving UTM settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to save UTM settings. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const addCustomParameter = () => {
    setConfig(prev => ({
      ...prev,
      custom_parameters: [...(prev.custom_parameters || []), { key: '', value: '' }]
    }));
  };

  const removeCustomParameter = (index: number) => {
    setConfig(prev => ({
      ...prev,
      custom_parameters: prev.custom_parameters?.filter((_, i) => i !== index) || []
    }));
  };

  const updateCustomParameter = (index: number, key: string, value: string) => {
    setConfig(prev => ({
      ...prev,
      custom_parameters: prev.custom_parameters?.map((param, i) => 
        i === index ? { key, value } : param
      ) || []
    }));
  };

  const generatePreviewURL = () => {
    const baseURL = `${window.location.origin}/booking/${calendarId}`;
    const params = new URLSearchParams();
    
    if (config.utm_source) params.append('utm_source', config.utm_source);
    if (config.utm_medium) params.append('utm_medium', config.utm_medium);
    if (config.utm_campaign) params.append('utm_campaign', config.utm_campaign);
    if (config.utm_term) params.append('utm_term', config.utm_term);
    if (config.utm_content) params.append('utm_content', config.utm_content);
    
    config.custom_parameters?.forEach(param => {
      if (param.key && param.value) {
        params.append(param.key, param.value);
      }
    });

    return params.toString() ? `${baseURL}?${params.toString()}` : baseURL;
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="w-5 h-5" />
            UTM Campaign Tracking
          </CardTitle>
          <CardDescription>
            Configure UTM parameters to track the source and performance of your booking links across different marketing campaigns.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="utm-tracking">Enable UTM Tracking</Label>
              <p className="text-sm text-muted-foreground">
                Automatically capture UTM parameters from visitors
              </p>
            </div>
            <Switch
              id="utm-tracking"
              checked={config.utm_tracking_enabled}
              onCheckedChange={(checked) => setConfig(prev => ({ ...prev, utm_tracking_enabled: checked }))}
            />
          </div>

          {config.utm_tracking_enabled && (
            <>
              <Separator />
              
              <Alert>
                <Info className="h-4 w-4" />
                <AlertDescription>
                  UTM parameters will be automatically captured when visitors arrive at your booking page with these parameters in the URL. 
                  You can also set default values below that will be used when generating tracking links.
                </AlertDescription>
              </Alert>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="utm-source">UTM Source</Label>
                  <Input
                    id="utm-source"
                    placeholder="e.g., google, facebook, newsletter"
                    value={config.utm_source}
                    onChange={(e) => setConfig(prev => ({ ...prev, utm_source: e.target.value }))}
                  />
                  <p className="text-xs text-muted-foreground">Identifies the source (google, facebook, email)</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="utm-medium">UTM Medium</Label>
                  <Input
                    id="utm-medium"
                    placeholder="e.g., cpc, banner, email"
                    value={config.utm_medium}
                    onChange={(e) => setConfig(prev => ({ ...prev, utm_medium: e.target.value }))}
                  />
                  <p className="text-xs text-muted-foreground">Identifies the medium (cpc, banner, email)</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="utm-campaign">UTM Campaign</Label>
                  <Input
                    id="utm-campaign"
                    placeholder="e.g., spring_sale, product_launch"
                    value={config.utm_campaign}
                    onChange={(e) => setConfig(prev => ({ ...prev, utm_campaign: e.target.value }))}
                  />
                  <p className="text-xs text-muted-foreground">Identifies the campaign</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="utm-term">UTM Term</Label>
                  <Input
                    id="utm-term"
                    placeholder="e.g., running+shoes"
                    value={config.utm_term}
                    onChange={(e) => setConfig(prev => ({ ...prev, utm_term: e.target.value }))}
                  />
                  <p className="text-xs text-muted-foreground">Identifies paid search keywords</p>
                </div>

                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="utm-content">UTM Content</Label>
                  <Input
                    id="utm-content"
                    placeholder="e.g., logolink, textlink"
                    value={config.utm_content}
                    onChange={(e) => setConfig(prev => ({ ...prev, utm_content: e.target.value }))}
                  />
                  <p className="text-xs text-muted-foreground">Differentiates similar content or links</p>
                </div>
              </div>

              <Separator />

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Custom Parameters</Label>
                    <p className="text-sm text-muted-foreground">Add custom tracking parameters beyond standard UTM</p>
                  </div>
                  <Button variant="outline" size="sm" onClick={addCustomParameter}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Parameter
                  </Button>
                </div>

                {config.custom_parameters?.map((param, index) => (
                  <div key={index} className="flex gap-2 items-end">
                    <div className="flex-1">
                      <Label>Parameter Key</Label>
                      <Input
                        placeholder="e.g., source_id"
                        value={param.key}
                        onChange={(e) => updateCustomParameter(index, e.target.value, param.value)}
                      />
                    </div>
                    <div className="flex-1">
                      <Label>Default Value</Label>
                      <Input
                        placeholder="e.g., homepage_banner"
                        value={param.value}
                        onChange={(e) => updateCustomParameter(index, param.key, e.target.value)}
                      />
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => removeCustomParameter(index)}
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                ))}
              </div>

              <Separator />

              <div className="space-y-4">
                <Label>Preview URL with UTM Parameters</Label>
                <div className="p-3 bg-muted rounded-lg">
                  <code className="text-sm break-all">{generatePreviewURL()}</code>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      <div className="flex justify-end">
        <Button onClick={saveUTMSettings} disabled={isSaving}>
          {isSaving ? 'Saving...' : 'Save UTM Settings'}
        </Button>
      </div>
    </div>
  );
}