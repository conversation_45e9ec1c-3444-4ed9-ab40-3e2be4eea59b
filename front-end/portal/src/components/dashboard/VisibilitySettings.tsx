import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/integrations/supabase/client';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { Eye, Save, Loader2, ExternalLink, Settings } from 'lucide-react';

interface Calendar {
  id: string;
  name: string;
  description: string;
  is_active: boolean;
  availability_schedule_id: string | null;
  booking_delay_hours: number;
  send_auto_invite: boolean;
  google_meet_enabled: boolean;
  user_id: string;
}

interface AvailabilitySchedule {
  id: string;
  name: string;
  description: string | null;
  is_default: boolean;
}

interface VisibilitySettingsProps {
  calendarId: string;
}

export const VisibilitySettings = ({ calendarId }: VisibilitySettingsProps) => {
  const { user } = useAuth();
  const [calendar, setCalendar] = useState<Calendar | null>(null);
  const [availabilitySchedules, setAvailabilitySchedules] = useState<AvailabilitySchedule[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [isActive, setIsActive] = useState(true);
  const [selectedScheduleId, setSelectedScheduleId] = useState<string | null>(null);
  const [bookingDelayHours, setBookingDelayHours] = useState(0);
  const [sendAutoInvite, setSendAutoInvite] = useState(false);
  const [googleMeetEnabled, setGoogleMeetEnabled] = useState(false);

  useEffect(() => {
    if (calendarId && user) {
      loadCalendar();
      loadAvailabilitySchedules();
    }
  }, [calendarId, user]);

  const loadAvailabilitySchedules = async () => {
    try {
      const { data, error } = await supabase
        .from('availability_schedules')
        .select('id, name, description, is_default')
        .eq('user_id', user?.id)
        .order('is_default', { ascending: false })
        .order('name');

      if (error) {
        console.error('Error loading availability schedules:', error);
        return;
      }

      setAvailabilitySchedules(data || []);
    } catch (error) {
      console.error('Error loading availability schedules:', error);
    }
  };

  const loadCalendar = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('calendars')
        .select('*')
        .eq('id', calendarId)
        .eq('user_id', user?.id)
        .single();

      if (error) {
        console.error('Error loading calendar:', error);
        toast.error('Failed to load calendar settings');
        return;
      }

      setCalendar(data);
      setName(data.name || '');
      setDescription(data.description || '');
      setIsActive(data.is_active);
      setSelectedScheduleId(data.availability_schedule_id);
      setBookingDelayHours(data.booking_delay_hours || 0);
      setSendAutoInvite(data.send_auto_invite || false);
      setGoogleMeetEnabled(data.google_meet_enabled || false);
    } catch (error) {
      console.error('Error loading calendar:', error);
      toast.error('Failed to load calendar settings');
    } finally {
      setLoading(false);
    }
  };

  const saveSettings = async () => {
    if (!calendar || !user) return;

    try {
      setSaving(true);
      const { error } = await supabase
        .from('calendars')
        .update({
          name: name.trim(),
          description: description.trim(),
          is_active: isActive,
          availability_schedule_id: selectedScheduleId,
          booking_delay_hours: bookingDelayHours,
          send_auto_invite: sendAutoInvite,
          google_meet_enabled: googleMeetEnabled,
          updated_at: new Date().toISOString()
        })
        .eq('id', calendarId)
        .eq('user_id', user.id);

      if (error) {
        console.error('Error updating calendar:', error);
        toast.error('Failed to update calendar settings');
        return;
      }

      setCalendar({
        ...calendar,
        name: name.trim(),
        description: description.trim(),
        is_active: isActive,
        availability_schedule_id: selectedScheduleId,
        booking_delay_hours: bookingDelayHours,
        send_auto_invite: sendAutoInvite,
        google_meet_enabled: googleMeetEnabled
      });

      toast.success('Calendar settings updated successfully');
    } catch (error) {
      console.error('Error updating calendar:', error);
      toast.error('Failed to update calendar settings');
    } finally {
      setSaving(false);
    }
  };

  const openBookingPage = () => {
    const bookingUrl = `${window.location.origin}/booking/${calendarId}`;
    window.open(bookingUrl, '_blank');
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Eye className="w-6 h-6 text-primary" />
          <h2 className="text-2xl font-bold">Visibility Settings</h2>
        </div>
        <Card className="animate-pulse">
          <CardContent className="p-6">
            <div className="space-y-4">
              <div className="h-4 bg-muted rounded w-1/4"></div>
              <div className="h-10 bg-muted rounded"></div>
              <div className="h-4 bg-muted rounded w-1/4"></div>
              <div className="h-24 bg-muted rounded"></div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!calendar) {
    return (
      <div className="space-y-6">
        <div className="flex items-center gap-2">
          <Eye className="w-6 h-6 text-primary" />
          <h2 className="text-2xl font-bold">Visibility Settings</h2>
        </div>
        <Card>
          <CardContent className="p-12 text-center">
            <p className="text-muted-foreground">Calendar not found or you don't have permission to edit it.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Eye className="w-6 h-6 text-primary" />
          <div>
            <h2 className="text-2xl font-bold">Visibility Settings</h2>
            <p className="text-muted-foreground">Configure how your calendar appears to visitors</p>
          </div>
        </div>
        <Button 
          variant="outline" 
          size="sm" 
          onClick={openBookingPage}
          className="flex items-center gap-2"
        >
          <ExternalLink className="w-4 h-4" />
          Preview
        </Button>
      </div>

      {/* Calendar Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Calendar Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="calendar-name">Calendar Name</Label>
            <Input
              id="calendar-name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter calendar name"
              maxLength={100}
            />
            <p className="text-xs text-muted-foreground">
              This is the name visitors will see when booking appointments
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="calendar-description">Description (Optional)</Label>
            <Input
              id="calendar-description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Brief description of this calendar"
              maxLength={200}
            />
            <p className="text-xs text-muted-foreground">
              A short description to help visitors understand what this calendar is for
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Calendar Status */}
      <Card>
        <CardHeader>
          <CardTitle>Calendar Status</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label htmlFor="calendar-active">Calendar Active</Label>
              <p className="text-sm text-muted-foreground">
                When enabled, visitors can book appointments. When disabled, the booking page will show as unavailable.
              </p>
            </div>
            <Switch
              id="calendar-active"
              checked={isActive}
              onCheckedChange={setIsActive}
            />
          </div>
        </CardContent>
      </Card>

      {/* Calendar Link */}
      <Card>
        <CardHeader>
          <CardTitle>Booking Link</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label>Your Booking URL</Label>
            <div className="flex gap-2">
              <Input
                value={`${window.location.origin}/booking/${calendarId}`}
                readOnly
                className="font-mono text-sm"
              />
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => {
                  navigator.clipboard.writeText(`${window.location.origin}/booking/${calendarId}`);
                  toast.success('Calendar link copied to clipboard');
                }}
              >
                Copy
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">
              Share this link with others so they can book appointments with you
            </p>
          </div>
        </CardContent>
      </Card>

        {/* Calendar Settings */}
        <Card>
          <CardHeader>
            <CardTitle>Booking Settings</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="booking-delay">Delay in booking availability (hours)</Label>
              <Input
                id="booking-delay"
                type="number"
                min="0"
                max="168"
                value={bookingDelayHours}
                onChange={(e) => setBookingDelayHours(parseInt(e.target.value) || 0)}
                placeholder="0"
              />
              <p className="text-xs text-muted-foreground">
                Only allow users to book appointments {bookingDelayHours > 0 ? `${bookingDelayHours} hours` : 'immediately'} from now
              </p>
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="auto-invite">Send auto-invite to invitee</Label>
                <p className="text-sm text-muted-foreground">
                  Automatically send calendar invitations to people who book appointments
                </p>
              </div>
              <Switch
                id="auto-invite"
                checked={sendAutoInvite}
                onCheckedChange={setSendAutoInvite}
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="google-meet">Google Meet Integration</Label>
                <p className="text-sm text-muted-foreground">
                  Automatically generate Google Meet links for bookings
                </p>
              </div>
              <Switch
                id="google-meet"
                checked={googleMeetEnabled}
                onCheckedChange={setGoogleMeetEnabled}
              />
            </div>
          </CardContent>
        </Card>

        {/* Update VisibilitySettings to include availability schedule selection */}
        <Card>
          <CardHeader>
            <CardTitle>Availability Schedule</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Select Availability Schedule</Label>
              <div className="flex gap-2">
                <Select value={selectedScheduleId || ''} onValueChange={setSelectedScheduleId}>
                  <SelectTrigger className="flex-1">
                    <SelectValue placeholder="Select an availability schedule" />
                  </SelectTrigger>
                  <SelectContent>
                    {availabilitySchedules.map((schedule) => (
                      <SelectItem key={schedule.id} value={schedule.id}>
                        {schedule.name} {schedule.is_default ? '(Default)' : ''}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => window.open('/availability', '_blank')}
                  className="flex items-center gap-2"
                >
                  <Settings className="w-4 h-4" />
                  Manage
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                Choose when visitors can book appointments for this calendar
              </p>
            </div>
          </CardContent>
        </Card>
        {/* Save Button */}
        <div className="flex justify-end">
          <Button 
            onClick={saveSettings}
            disabled={saving || !name.trim()}
            className="min-w-[120px]"
          >
            {saving ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
    </div>
  );
};