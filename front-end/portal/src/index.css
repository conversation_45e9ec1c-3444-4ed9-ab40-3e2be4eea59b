@tailwind base;
@tailwind components;
@tailwind utilities;

/* Calendly Alternative Design System - Warm Orange Theme */

@layer base {
  :root {
    /* Warm background palette */
    --background: 35 25% 95%;
    --foreground: 15 10% 15%;

    /* Card and surface colors */
    --card: 0 0% 100%;
    --card-foreground: 15 10% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 15 10% 15%;

    /* Primary orange brand colors */
    --primary: 16 100% 60%;
    --primary-foreground: 0 0% 100%;
    --primary-light: 25 100% 75%;
    --primary-dark: 10 100% 45%;

    /* Secondary warm colors */
    --secondary: 35 35% 90%;
    --secondary-foreground: 15 10% 15%;

    /* Muted warm tones */
    --muted: 35 20% 92%;
    --muted-foreground: 25 8% 45%;

    /* Accent teal (for CTAs) */
    --accent: 170 50% 45%;
    --accent-foreground: 0 0% 100%;
    --accent-light: 170 40% 55%;

    /* Status colors */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    --success: 140 50% 45%;
    --success-foreground: 0 0% 100%;

    /* Border and input colors */
    --border: 35 15% 85%;
    --input: 35 15% 88%;
    --ring: 16 100% 60%;

    --radius: 0.75rem;

    /* Custom gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-light)));
    --gradient-warm: linear-gradient(135deg, hsl(35, 40%, 95%), hsl(25, 30%, 90%));
    --gradient-hero: linear-gradient(135deg, hsl(35, 35%, 92%), hsl(25, 45%, 88%));
    
    /* Organic shapes and animations */
    --blob-1: polygon(30% 0%, 70% 0%, 100% 30%, 100% 70%, 70% 100%, 30% 100%, 0% 70%, 0% 30%);
    --blob-2: polygon(25% 0%, 75% 0%, 100% 38%, 82% 100%, 18% 100%, 0% 38%);
    
    /* Shadows */
    --shadow-soft: 0 4px 20px -4px hsl(var(--primary) / 0.15);
    --shadow-card: 0 2px 10px -2px hsl(15, 10%, 15% / 0.1);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}