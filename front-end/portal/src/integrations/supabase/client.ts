// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://nhifxkfxbgzesrgkfryd.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im5oaWZ4a2Z4Ymd6ZXNyZ2tmcnlkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTUxNzUyOTEsImV4cCI6MjA3MDc1MTI5MX0.j1LSlf8tF5wVWTP2BOAo39yjgRiO7yAffhImdB7Esus";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});