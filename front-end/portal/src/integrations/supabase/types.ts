export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "13.0.4"
  }
  public: {
    Tables: {
      analytics_events: {
        Row: {
          booking_id: string | null
          calendar_id: string | null
          created_at: string
          event_type: string
          id: string
          ip_address: string | null
          metadata: Json | null
          page_url: string | null
          user_agent: string | null
        }
        Insert: {
          booking_id?: string | null
          calendar_id?: string | null
          created_at?: string
          event_type: string
          id?: string
          ip_address?: string | null
          metadata?: Json | null
          page_url?: string | null
          user_agent?: string | null
        }
        Update: {
          booking_id?: string | null
          calendar_id?: string | null
          created_at?: string
          event_type?: string
          id?: string
          ip_address?: string | null
          metadata?: Json | null
          page_url?: string | null
          user_agent?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "analytics_events_booking_id_fkey"
            columns: ["booking_id"]
            isOneToOne: false
            referencedRelation: "bookings"
            referencedColumns: ["id"]
          },
        ]
      }
      apple_calendar_integrations: {
        Row: {
          account_name: string
          caldav_url: string
          calendar_id: string | null
          created_at: string
          id: string
          last_sync_at: string | null
          password: string
          sync_enabled: boolean
          updated_at: string
          user_id: string
          username: string
        }
        Insert: {
          account_name: string
          caldav_url: string
          calendar_id?: string | null
          created_at?: string
          id?: string
          last_sync_at?: string | null
          password: string
          sync_enabled?: boolean
          updated_at?: string
          user_id: string
          username: string
        }
        Update: {
          account_name?: string
          caldav_url?: string
          calendar_id?: string | null
          created_at?: string
          id?: string
          last_sync_at?: string | null
          password?: string
          sync_enabled?: boolean
          updated_at?: string
          user_id?: string
          username?: string
        }
        Relationships: []
      }
      availability: {
        Row: {
          calendar_id: string
          created_at: string
          day_of_week: number
          end_time: string
          id: string
          start_time: string
        }
        Insert: {
          calendar_id: string
          created_at?: string
          day_of_week: number
          end_time: string
          id?: string
          start_time: string
        }
        Update: {
          calendar_id?: string
          created_at?: string
          day_of_week?: number
          end_time?: string
          id?: string
          start_time?: string
        }
        Relationships: [
          {
            foreignKeyName: "availability_calendar_id_fkey"
            columns: ["calendar_id"]
            isOneToOne: false
            referencedRelation: "calendars"
            referencedColumns: ["id"]
          },
        ]
      }
      availability_schedule_rules: {
        Row: {
          created_at: string
          day_of_week: number
          end_time: string
          id: string
          schedule_id: string
          start_time: string
        }
        Insert: {
          created_at?: string
          day_of_week: number
          end_time: string
          id?: string
          schedule_id: string
          start_time: string
        }
        Update: {
          created_at?: string
          day_of_week?: number
          end_time?: string
          id?: string
          schedule_id?: string
          start_time?: string
        }
        Relationships: [
          {
            foreignKeyName: "availability_schedule_rules_schedule_id_fkey"
            columns: ["schedule_id"]
            isOneToOne: false
            referencedRelation: "availability_schedules"
            referencedColumns: ["id"]
          },
        ]
      }
      availability_schedules: {
        Row: {
          created_at: string
          description: string | null
          id: string
          is_default: boolean
          name: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          description?: string | null
          id?: string
          is_default?: boolean
          name: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          description?: string | null
          id?: string
          is_default?: boolean
          name?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      bookings: {
        Row: {
          attendee_email: string
          attendee_name: string
          booking_date: string
          booking_time: string
          calendar_id: string
          created_at: string
          google_meet_link: string | null
          id: string
          notes: string | null
          status: string | null
        }
        Insert: {
          attendee_email: string
          attendee_name: string
          booking_date: string
          booking_time: string
          calendar_id: string
          created_at?: string
          google_meet_link?: string | null
          id?: string
          notes?: string | null
          status?: string | null
        }
        Update: {
          attendee_email?: string
          attendee_name?: string
          booking_date?: string
          booking_time?: string
          calendar_id?: string
          created_at?: string
          google_meet_link?: string | null
          id?: string
          notes?: string | null
          status?: string | null
        }
        Relationships: []
      }
      calendars: {
        Row: {
          availability_schedule_id: string | null
          booking_delay_hours: number | null
          color: string | null
          conversion_tracking: boolean | null
          created_at: string
          data_layer_config: Json | null
          description: string | null
          duration: number
          google_meet_auto_generate: boolean | null
          google_meet_enabled: boolean | null
          header_description: string | null
          header_image_url: string | null
          header_title: string | null
          id: string
          is_active: boolean | null
          name: string
          send_auto_invite: boolean | null
          show_header: boolean | null
          thank_you_config: Json | null
          theme_id: string | null
          updated_at: string
          user_id: string
          utm_config: Json | null
          whats_next_description: string | null
          whats_next_enabled: boolean | null
          whats_next_title: string | null
        }
        Insert: {
          availability_schedule_id?: string | null
          booking_delay_hours?: number | null
          color?: string | null
          conversion_tracking?: boolean | null
          created_at?: string
          data_layer_config?: Json | null
          description?: string | null
          duration?: number
          google_meet_auto_generate?: boolean | null
          google_meet_enabled?: boolean | null
          header_description?: string | null
          header_image_url?: string | null
          header_title?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          send_auto_invite?: boolean | null
          show_header?: boolean | null
          thank_you_config?: Json | null
          theme_id?: string | null
          updated_at?: string
          user_id: string
          utm_config?: Json | null
          whats_next_description?: string | null
          whats_next_enabled?: boolean | null
          whats_next_title?: string | null
        }
        Update: {
          availability_schedule_id?: string | null
          booking_delay_hours?: number | null
          color?: string | null
          conversion_tracking?: boolean | null
          created_at?: string
          data_layer_config?: Json | null
          description?: string | null
          duration?: number
          google_meet_auto_generate?: boolean | null
          google_meet_enabled?: boolean | null
          header_description?: string | null
          header_image_url?: string | null
          header_title?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          send_auto_invite?: boolean | null
          show_header?: boolean | null
          thank_you_config?: Json | null
          theme_id?: string | null
          updated_at?: string
          user_id?: string
          utm_config?: Json | null
          whats_next_description?: string | null
          whats_next_enabled?: boolean | null
          whats_next_title?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "calendars_availability_schedule_id_fkey"
            columns: ["availability_schedule_id"]
            isOneToOne: false
            referencedRelation: "availability_schedules"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_calendars_theme"
            columns: ["theme_id"]
            isOneToOne: false
            referencedRelation: "themes"
            referencedColumns: ["id"]
          },
        ]
      }
      conversion_events: {
        Row: {
          booking_id: string | null
          calendar_id: string | null
          conversion_value: number | null
          created_at: string
          event_type: string
          funnel_step: string | null
          id: string
          ip_address: string | null
          metadata: Json | null
          referrer: string | null
          user_agent: string | null
          utm_campaign: string | null
          utm_medium: string | null
          utm_source: string | null
        }
        Insert: {
          booking_id?: string | null
          calendar_id?: string | null
          conversion_value?: number | null
          created_at?: string
          event_type: string
          funnel_step?: string | null
          id?: string
          ip_address?: string | null
          metadata?: Json | null
          referrer?: string | null
          user_agent?: string | null
          utm_campaign?: string | null
          utm_medium?: string | null
          utm_source?: string | null
        }
        Update: {
          booking_id?: string | null
          calendar_id?: string | null
          conversion_value?: number | null
          created_at?: string
          event_type?: string
          funnel_step?: string | null
          id?: string
          ip_address?: string | null
          metadata?: Json | null
          referrer?: string | null
          user_agent?: string | null
          utm_campaign?: string | null
          utm_medium?: string | null
          utm_source?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "conversion_events_booking_id_fkey"
            columns: ["booking_id"]
            isOneToOne: false
            referencedRelation: "bookings"
            referencedColumns: ["id"]
          },
        ]
      }
      global_availability: {
        Row: {
          created_at: string
          date: string
          end_time: string
          id: string
          is_recurring: boolean
          recurrence_pattern: string | null
          start_time: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          date: string
          end_time: string
          id?: string
          is_recurring?: boolean
          recurrence_pattern?: string | null
          start_time: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          date?: string
          end_time?: string
          id?: string
          is_recurring?: boolean
          recurrence_pattern?: string | null
          start_time?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      google_calendar_integrations: {
        Row: {
          access_token: string
          calendar_id: string | null
          created_at: string
          google_account_email: string
          id: string
          last_sync_at: string | null
          refresh_token: string
          sync_enabled: boolean
          updated_at: string
          user_id: string
        }
        Insert: {
          access_token: string
          calendar_id?: string | null
          created_at?: string
          google_account_email: string
          id?: string
          last_sync_at?: string | null
          refresh_token: string
          sync_enabled?: boolean
          updated_at?: string
          user_id: string
        }
        Update: {
          access_token?: string
          calendar_id?: string | null
          created_at?: string
          google_account_email?: string
          id?: string
          last_sync_at?: string | null
          refresh_token?: string
          sync_enabled?: boolean
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      holidays: {
        Row: {
          country_code: string | null
          created_at: string
          date: string
          description: string | null
          id: string
          is_public_holiday: boolean
          name: string
          updated_at: string
          user_id: string
        }
        Insert: {
          country_code?: string | null
          created_at?: string
          date: string
          description?: string | null
          id?: string
          is_public_holiday?: boolean
          name: string
          updated_at?: string
          user_id: string
        }
        Update: {
          country_code?: string | null
          created_at?: string
          date?: string
          description?: string | null
          id?: string
          is_public_holiday?: boolean
          name?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      profiles: {
        Row: {
          avatar_url: string | null
          created_at: string
          email: string | null
          full_name: string | null
          id: string
          updated_at: string
          user_id: string
        }
        Insert: {
          avatar_url?: string | null
          created_at?: string
          email?: string | null
          full_name?: string | null
          id?: string
          updated_at?: string
          user_id: string
        }
        Update: {
          avatar_url?: string | null
          created_at?: string
          email?: string | null
          full_name?: string | null
          id?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      themes: {
        Row: {
          colors: Json
          created_at: string
          description: string | null
          fonts: Json
          id: string
          is_premium: boolean | null
          layout_settings: Json
          name: string
          preview_image_url: string | null
          user_id: string | null
        }
        Insert: {
          colors?: Json
          created_at?: string
          description?: string | null
          fonts?: Json
          id?: string
          is_premium?: boolean | null
          layout_settings?: Json
          name: string
          preview_image_url?: string | null
          user_id?: string | null
        }
        Update: {
          colors?: Json
          created_at?: string
          description?: string | null
          fonts?: Json
          id?: string
          is_premium?: boolean | null
          layout_settings?: Json
          name?: string
          preview_image_url?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      user_settings: {
        Row: {
          created_at: string
          google_meet_enabled: boolean
          id: string
          show_header: boolean | null
          theme_id: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          google_meet_enabled?: boolean
          id?: string
          show_header?: boolean | null
          theme_id?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          google_meet_enabled?: boolean
          id?: string
          show_header?: boolean | null
          theme_id?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      working_hours: {
        Row: {
          created_at: string
          day_of_week: number
          end_time: string
          id: string
          is_enabled: boolean
          start_time: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          day_of_week: number
          end_time: string
          id?: string
          is_enabled?: boolean
          start_time: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          day_of_week?: number
          end_time?: string
          id?: string
          is_enabled?: boolean
          start_time?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
