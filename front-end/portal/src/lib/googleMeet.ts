/**
 * Google Meet link generation utilities
 */

export interface GoogleMeetLink {
  url: string;
  meetingId: string;
}

/**
 * Generates a unique Google Meet link
 * Since we can't use the full Google Calendar API without OAuth,
 * we'll generate meet.google.com links with unique identifiers
 */
export function generateGoogleMeetLink(
  attendeeName: string,
  bookingDate: string,
  bookingTime: string
): GoogleMeetLink {
  // Generate a unique meeting ID based on timestamp and random string
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 15);
  const meetingId = `${timestamp}-${randomString}`;
  
  // Create a descriptive meeting title
  const meetingTitle = encodeURIComponent(
    `Meeting with ${attendeeName} - ${bookingDate} at ${bookingTime}`
  );
  
  // Generate the Google Meet URL
  const url = `https://meet.google.com/new?authuser=0&hs=122&pli=1&title=${meetingTitle}`;
  
  return {
    url,
    meetingId,
  };
}

/**
 * Creates a direct Google Meet link without parameters
 * This creates an instant meeting room
 */
export function createInstantGoogleMeetLink(): GoogleMeetLink {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 15);
  const meetingId = `instant-${timestamp}-${randomString}`;
  
  // This creates a new Google Meet room instantly
  const url = 'https://meet.google.com/new';
  
  return {
    url,
    meetingId,
  };
}

/**
 * Formats a Google Meet link for display
 */
export function formatMeetLink(link: string): string {
  if (link.includes('meet.google.com')) {
    return link;
  }
  return `https://meet.google.com/${link}`;
}

/**
 * Validates if a string is a valid Google Meet link
 */
export function isValidGoogleMeetLink(url: string): boolean {
  try {
    const urlObj = new URL(url);
    return urlObj.hostname === 'meet.google.com' || 
           urlObj.hostname === 'meet.google.com';
  } catch {
    return false;
  }
}